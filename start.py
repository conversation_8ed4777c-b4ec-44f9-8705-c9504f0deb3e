# -*- coding: utf-8 -*-
"""
音视频转字幕应用启动脚本
用于启动Flask应用，包含环境检查和初始化
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("错误：需要Python 3.8或更高版本")
        print(f"当前版本：{version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✓ Python版本检查通过：{version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """检查依赖包"""
    try:
        import flask
        print(f"✓ Flask版本：{flask.__version__}")
        
        import moviepy
        print(f"✓ MoviePy已安装")
        
        import requests
        print(f"✓ Requests已安装")
        
        return True
    except ImportError as e:
        print(f"错误：缺少依赖包 - {e}")
        print("请运行：pip install -r requirements.txt")
        return False

def check_directories():
    """检查必要目录"""
    directories = ['uploads', 'temp', 'logs', 'static', 'templates']
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"✓ 创建目录：{directory}")
            except Exception as e:
                print(f"错误：无法创建目录 {directory} - {e}")
                return False
        else:
            print(f"✓ 目录存在：{directory}")
    
    return True

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误：依赖包安装失败 - {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("音视频转字幕应用启动检查")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查目录
    if not check_directories():
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        print("\n是否要自动安装依赖包？(y/n): ", end="")
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            if not install_dependencies():
                sys.exit(1)
        else:
            print("请手动安装依赖包：pip install -r requirements.txt")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("环境检查完成，启动应用...")
    print("=" * 50)
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    # 启动应用
    try:
        from app import app
        print("\n🚀 应用启动成功！")
        print("📱 访问地址：http://localhost:5000")
        print("📱 局域网访问：http://0.0.0.0:5000")
        print("⏹️  按 Ctrl+C 停止应用\n")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 应用启动失败：{e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
