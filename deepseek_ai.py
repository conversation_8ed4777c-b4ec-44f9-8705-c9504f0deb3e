# -*- coding: utf-8 -*-
"""
DeepSeek AI字幕优化服务模块
负责使用DeepSeek AI对字幕文本进行智能优化
"""

import json
import logging
import requests
import time
import re
from typing import List, Dict, Optional

class DeepSeekAI:
    """DeepSeek AI字幕优化服务"""
    
    def __init__(self, api_key: str, api_url: str = "https://api.deepseek.com/v1/chat/completions"):
        self.api_key = api_key
        self.api_url = api_url
        self.logger = logging.getLogger(__name__)
        
        if not api_key:
            raise Exception("DeepSeek API密钥未配置")
        
        # 设置请求头
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
        
        self.logger.info("DeepSeek AI服务初始化成功")
    
    def optimize_subtitle_text(self, srt_content: str, callback=None) -> Dict:
        """优化字幕文本"""
        try:
            self.logger.info("开始AI字幕优化")
            
            if callback:
                callback("正在解析字幕内容...")
            
            # 解析SRT内容
            subtitle_entries = self._parse_srt_content(srt_content)
            
            if not subtitle_entries:
                raise Exception("无法解析字幕内容")
            
            self.logger.info(f"解析到{len(subtitle_entries)}条字幕")
            
            if callback:
                callback("正在进行AI优化...")
            
            # 批量优化字幕文本
            optimized_entries = self._optimize_subtitle_entries(subtitle_entries, callback)
            
            if callback:
                callback("正在生成优化后的字幕...")
            
            # 重新生成SRT内容
            optimized_srt = self._generate_optimized_srt(optimized_entries)
            
            self.logger.info("AI字幕优化完成")
            
            return {
                'success': True,
                'original_srt': srt_content,
                'optimized_srt': optimized_srt,
                'original_count': len(subtitle_entries),
                'optimized_count': len(optimized_entries),
                'optimization_summary': self._generate_optimization_summary(subtitle_entries, optimized_entries)
            }
            
        except Exception as e:
            self.logger.error(f"AI字幕优化失败: {str(e)}")
            raise Exception(f"AI字幕优化失败: {str(e)}")
    
    def _parse_srt_content(self, srt_content: str) -> List[Dict]:
        """解析SRT内容"""
        try:
            entries = []
            lines = srt_content.strip().split('\n')
            
            current_entry = {}
            line_type = 'index'  # index, time, text
            
            for line in lines:
                line = line.strip()
                
                if not line:
                    if current_entry and 'text' in current_entry:
                        entries.append(current_entry)
                        current_entry = {}
                        line_type = 'index'
                    continue
                
                if line_type == 'index' and line.isdigit():
                    current_entry['index'] = int(line)
                    line_type = 'time'
                elif line_type == 'time' and '-->' in line:
                    times = line.split(' --> ')
                    current_entry['start_time'] = times[0].strip()
                    current_entry['end_time'] = times[1].strip()
                    current_entry['text'] = ''
                    line_type = 'text'
                elif line_type == 'text':
                    if current_entry['text']:
                        current_entry['text'] += '\n'
                    current_entry['text'] += line
            
            # 添加最后一个条目
            if current_entry and 'text' in current_entry:
                entries.append(current_entry)
            
            return entries
            
        except Exception as e:
            self.logger.error(f"解析SRT内容失败: {str(e)}")
            raise
    
    def _optimize_subtitle_entries(self, entries: List[Dict], callback=None) -> List[Dict]:
        """批量优化字幕条目"""
        try:
            optimized_entries = []
            batch_size = 5  # 每次处理5条字幕
            total_batches = (len(entries) + batch_size - 1) // batch_size
            
            for i in range(0, len(entries), batch_size):
                batch = entries[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                if callback:
                    callback(f"AI优化进行中... ({batch_num}/{total_batches})")
                
                # 优化当前批次
                optimized_batch = self._optimize_batch(batch)
                optimized_entries.extend(optimized_batch)
                
                # 避免API调用过于频繁
                if batch_num < total_batches:
                    time.sleep(1)
            
            return optimized_entries
            
        except Exception as e:
            self.logger.error(f"批量优化字幕失败: {str(e)}")
            raise
    
    def _optimize_batch(self, batch: List[Dict]) -> List[Dict]:
        """优化一批字幕条目"""
        try:
            # 构建优化提示词
            prompt = self._build_optimization_prompt(batch)
            
            # 调用DeepSeek API
            response = self._call_deepseek_api(prompt)
            
            # 解析优化结果
            optimized_batch = self._parse_optimization_result(batch, response)
            
            return optimized_batch
            
        except Exception as e:
            self.logger.error(f"优化批次失败: {str(e)}")
            # 如果优化失败，返回原始内容
            return batch
    
    def _build_optimization_prompt(self, batch: List[Dict]) -> str:
        """构建优化提示词"""
        try:
            # 提取文本内容
            texts = []
            for i, entry in enumerate(batch):
                texts.append(f"{i+1}. {entry['text']}")
            
            batch_text = '\n'.join(texts)
            
            prompt = f"""请对以下字幕文本进行优化，要求：

1. 保持原意不变，只优化语言表达
2. 修正语音识别可能的错误
3. 规范标点符号使用
4. 让语言更加自然流畅
5. 保持字幕的简洁性
6. 每条字幕保持独立完整
7. 返回格式与输入格式完全一致（序号. 内容）

原始字幕：
{batch_text}

请返回优化后的字幕："""

            return prompt
            
        except Exception as e:
            self.logger.error(f"构建优化提示词失败: {str(e)}")
            raise
    
    def _call_deepseek_api(self, prompt: str, max_retries: int = 3) -> str:
        """调用DeepSeek API"""
        try:
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的字幕优化专家，擅长优化语音识别生成的字幕文本，让其更加准确、自然、易读。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 2000,
                "top_p": 0.9,
                "frequency_penalty": 0,
                "presence_penalty": 0
            }
            
            for attempt in range(max_retries):
                try:
                    response = requests.post(
                        self.api_url,
                        headers=self.headers,
                        json=payload,
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        if 'choices' in result and result['choices']:
                            content = result['choices'][0]['message']['content']
                            self.logger.debug(f"API调用成功，返回内容长度: {len(content)}")
                            return content.strip()
                        else:
                            raise Exception("API返回格式错误")
                    
                    elif response.status_code == 429:
                        # 速率限制，等待后重试
                        wait_time = 2 ** attempt
                        self.logger.warning(f"API速率限制，等待{wait_time}秒后重试")
                        time.sleep(wait_time)
                        continue
                    
                    else:
                        error_msg = f"API调用失败，状态码: {response.status_code}"
                        if response.text:
                            error_msg += f", 错误信息: {response.text}"
                        raise Exception(error_msg)
                
                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"API调用超时，重试第{attempt + 1}次")
                        time.sleep(1)
                        continue
                    else:
                        raise Exception("API调用超时")
                
                except requests.exceptions.RequestException as e:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"网络请求失败，重试第{attempt + 1}次: {str(e)}")
                        time.sleep(1)
                        continue
                    else:
                        raise Exception(f"网络请求失败: {str(e)}")
            
            raise Exception("API调用失败，已达到最大重试次数")
            
        except Exception as e:
            self.logger.error(f"调用DeepSeek API失败: {str(e)}")
            raise
    
    def _parse_optimization_result(self, original_batch: List[Dict], api_response: str) -> List[Dict]:
        """解析优化结果"""
        try:
            optimized_batch = []
            
            # 使用正则表达式提取优化后的文本
            pattern = r'(\d+)\.\s*(.+?)(?=\n\d+\.|$)'
            matches = re.findall(pattern, api_response, re.DOTALL)
            
            for i, entry in enumerate(original_batch):
                optimized_entry = entry.copy()
                
                # 查找对应的优化文本
                optimized_text = None
                for match in matches:
                    if int(match[0]) == i + 1:
                        optimized_text = match[1].strip()
                        break
                
                if optimized_text:
                    # 清理优化后的文本
                    optimized_text = self._clean_optimized_text(optimized_text)
                    optimized_entry['text'] = optimized_text
                    optimized_entry['optimized'] = True
                else:
                    # 如果没有找到优化文本，保持原文
                    optimized_entry['optimized'] = False
                
                optimized_batch.append(optimized_entry)
            
            return optimized_batch
            
        except Exception as e:
            self.logger.error(f"解析优化结果失败: {str(e)}")
            # 如果解析失败，返回原始内容
            for entry in original_batch:
                entry['optimized'] = False
            return original_batch
    
    def _clean_optimized_text(self, text: str) -> str:
        """清理优化后的文本"""
        try:
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            
            # 移除可能的引号
            text = text.strip('"\'""''')
            
            # 确保标点符号正确
            text = re.sub(r'\s+([，。！？；：])', r'\1', text)
            
            return text.strip()
            
        except Exception as e:
            self.logger.error(f"清理优化文本失败: {str(e)}")
            return text
    
    def _generate_optimized_srt(self, optimized_entries: List[Dict]) -> str:
        """生成优化后的SRT内容"""
        try:
            srt_lines = []
            
            for entry in optimized_entries:
                srt_lines.append(str(entry['index']))
                srt_lines.append(f"{entry['start_time']} --> {entry['end_time']}")
                srt_lines.append(entry['text'])
                srt_lines.append('')  # 空行分隔
            
            return '\n'.join(srt_lines)
            
        except Exception as e:
            self.logger.error(f"生成优化SRT失败: {str(e)}")
            raise
    
    def _generate_optimization_summary(self, original_entries: List[Dict], optimized_entries: List[Dict]) -> Dict:
        """生成优化摘要"""
        try:
            optimized_count = sum(1 for entry in optimized_entries if entry.get('optimized', False))
            
            summary = {
                'total_entries': len(original_entries),
                'optimized_entries': optimized_count,
                'optimization_rate': round(optimized_count / len(original_entries) * 100, 1) if original_entries else 0,
                'improvements': [
                    '语言表达优化',
                    '标点符号规范',
                    '语音识别错误修正',
                    '语言流畅性提升'
                ]
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"生成优化摘要失败: {str(e)}")
            return {}

def create_deepseek_service(config):
    """创建DeepSeek AI服务实例"""
    try:
        api_key = config.get('DEEPSEEK_API_KEY')
        api_url = config.get('DEEPSEEK_API_URL', 'https://api.deepseek.com/v1/chat/completions')
        
        if not api_key:
            raise Exception("DeepSeek API密钥未配置")
        
        return DeepSeekAI(api_key, api_url)
        
    except Exception as e:
        logging.error(f"创建DeepSeek AI服务失败: {str(e)}")
        raise
