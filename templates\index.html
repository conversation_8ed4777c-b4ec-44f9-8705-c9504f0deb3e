<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.app_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-video me-2"></i>
                    {{ config.app_name }}
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        智能音视频转字幕工具
                    </span>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="container mt-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- 功能介绍卡片 -->
                    <div class="card mb-4">
                        <div class="card-body text-center">
                            <h2 class="card-title">
                                <i class="fas fa-magic text-primary me-2"></i>
                                智能音视频转字幕
                            </h2>
                            <p class="card-text text-muted">
                                支持多种音视频格式，使用腾讯云语音识别技术，配合AI智能优化，生成高质量SRT字幕文件
                            </p>
                        </div>
                    </div>

                    <!-- 文件上传区域 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                上传音视频文件
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 上传区域将在步骤二中完善 -->
                            <div class="upload-area text-center p-5 border border-dashed rounded">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>拖拽文件到此处或点击选择文件</h5>
                                <p class="text-muted">
                                    支持格式：{{ config.allowed_extensions }}<br>
                                    最大文件大小：{{ config.max_file_size_mb }}MB
                                </p>
                                <input type="file" id="fileInput" class="d-none" accept=".mp3,.wav,.m4a,.aac,.mp4,.avi,.mov,.mkv">
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-folder-open me-2"></i>
                                    选择文件
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 进度显示区域 -->
                    <div class="card mt-4" id="progressCard" style="display: none;">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>
                                处理进度
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="statusText" class="text-center">准备开始...</div>
                        </div>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="card mt-4" id="resultCard" style="display: none;">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                转换完成
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-success w-100" id="downloadOriginal">
                                        <i class="fas fa-download me-2"></i>
                                        下载原始字幕
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-primary w-100" id="downloadOptimized">
                                        <i class="fas fa-sparkles me-2"></i>
                                        下载AI优化字幕
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="bg-light mt-5 py-4">
            <div class="container text-center">
                <p class="text-muted mb-0">
                    &copy; 2025 音视频转字幕工具 - 基于腾讯云语音识别 & DeepSeek AI
                </p>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
