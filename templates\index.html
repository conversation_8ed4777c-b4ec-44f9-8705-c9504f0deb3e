<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.app_name }}</title>
    <meta name="description" content="智能音视频转字幕工具，支持多种格式，AI优化字幕质量">
    <meta name="keywords" content="音视频转字幕,语音识别,AI优化,SRT字幕">

    <!-- CSS 样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
</head>
<body>
    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner-large"></div>
            <h4 class="mt-3">正在处理中...</h4>
            <p class="text-muted">请耐心等待，不要关闭页面</p>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-gradient">
            <div class="container">
                <a class="navbar-brand animate__animated animate__fadeInLeft" href="#">
                    <i class="fas fa-video me-2"></i>
                    {{ config.app_name }}
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <div class="navbar-nav ms-auto">
                        <a class="nav-link" href="#features">
                            <i class="fas fa-star me-1"></i>功能特点
                        </a>
                        <a class="nav-link" href="#usage">
                            <i class="fas fa-question-circle me-1"></i>使用说明
                        </a>
                        <span class="navbar-text ms-3">
                            <i class="fas fa-magic me-1"></i>智能音视频转字幕工具
                        </span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="container mt-4">
            <!-- 提示消息区域 -->
            <div id="alertContainer"></div>

            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <!-- 功能介绍卡片 -->
                    <div class="card mb-4 animate__animated animate__fadeInUp">
                        <div class="card-body text-center">
                            <h1 class="card-title display-6 mb-3">
                                <i class="fas fa-magic text-primary me-2"></i>
                                智能音视频转字幕
                            </h1>
                            <p class="card-text lead text-muted mb-4">
                                支持多种音视频格式，使用腾讯云语音识别技术，配合DeepSeek AI智能优化，生成高质量SRT字幕文件
                            </p>

                            <!-- 功能特点 -->
                            <div class="row text-start" id="features">
                                <div class="col-md-4 mb-3">
                                    <div class="feature-item">
                                        <i class="fas fa-file-audio text-success fa-2x mb-2"></i>
                                        <h6>多格式支持</h6>
                                        <small class="text-muted">支持MP3、WAV、MP4、AVI等15种格式</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="feature-item">
                                        <i class="fas fa-brain text-info fa-2x mb-2"></i>
                                        <h6>AI智能优化</h6>
                                        <small class="text-muted">DeepSeek AI自动优化字幕文本质量</small>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="feature-item">
                                        <i class="fas fa-download text-warning fa-2x mb-2"></i>
                                        <h6>标准格式</h6>
                                        <small class="text-muted">生成标准SRT格式字幕文件</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件上传区域 -->
                    <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                上传音视频文件
                            </h5>
                            <div class="upload-options">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="aiOptimizeSwitch" checked>
                                    <label class="form-check-label" for="aiOptimizeSwitch">
                                        <i class="fas fa-sparkles me-1"></i>AI优化
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 文件上传区域 -->
                            <div class="upload-area text-center p-5 border border-dashed rounded" id="uploadArea">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt fa-4x text-muted mb-3 upload-icon"></i>
                                    <h4 class="upload-title">拖拽文件到此处或点击选择文件</h4>
                                    <p class="text-muted mb-3">
                                        支持格式：MP3、WAV、M4A、AAC、MP4、AVI、MOV、MKV等<br>
                                        最大文件大小：{{ config.max_file_size_mb }}MB
                                    </p>

                                    <!-- 文件输入 -->
                                    <input type="file" id="fileInput" class="d-none"
                                           accept=".mp3,.wav,.m4a,.aac,.flac,.ogg,.wma,.mp4,.avi,.mov,.mkv,.wmv,.flv,.webm,.3gp">

                                    <!-- 上传按钮 -->
                                    <div class="upload-buttons">
                                        <button type="button" class="btn btn-primary btn-lg me-2" id="selectFileBtn">
                                            <i class="fas fa-folder-open me-2"></i>
                                            选择文件
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-lg" id="clearFileBtn" style="display: none;">
                                            <i class="fas fa-times me-2"></i>
                                            清除
                                        </button>
                                    </div>
                                </div>

                                <!-- 文件预览区域 -->
                                <div class="file-preview" id="filePreview" style="display: none;">
                                    <div class="file-info-card">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <i class="fas fa-file-video fa-3x text-primary"></i>
                                            </div>
                                            <div class="col">
                                                <h6 class="mb-1" id="fileName">文件名</h6>
                                                <p class="mb-1 text-muted">
                                                    <span id="fileSize">大小</span> |
                                                    <span id="fileType">类型</span>
                                                </p>
                                                <div class="progress mt-2" style="height: 8px;">
                                                    <div class="progress-bar bg-success" id="uploadProgress" style="width: 0%"></div>
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <button class="btn btn-success btn-lg" id="startProcessBtn">
                                                    <i class="fas fa-play me-2"></i>
                                                    开始转换
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 使用说明 -->
                            <div class="mt-4" id="usage">
                                <h6 class="text-muted mb-3">
                                    <i class="fas fa-info-circle me-2"></i>使用说明
                                </h6>
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <div class="usage-step">
                                            <span class="step-number">1</span>
                                            <small>选择或拖拽音视频文件</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <div class="usage-step">
                                            <span class="step-number">2</span>
                                            <small>等待语音识别处理</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <div class="usage-step">
                                            <span class="step-number">3</span>
                                            <small>AI智能优化字幕</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <div class="usage-step">
                                            <span class="step-number">4</span>
                                            <small>下载SRT字幕文件</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 进度显示区域 -->
                    <div class="card mt-4 animate__animated animate__fadeInUp" id="progressCard" style="display: none; animation-delay: 0.4s;">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>
                                处理进度
                            </h5>
                            <div class="ms-auto">
                                <small class="text-light" id="timeElapsed">已用时: 00:00</small>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 主进度条 -->
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" id="mainProgressBar" style="width: 0%">
                                    <span id="progressPercentage">0%</span>
                                </div>
                            </div>

                            <!-- 状态信息 -->
                            <div class="row">
                                <div class="col-md-8">
                                    <div id="statusText" class="status-text">
                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                        准备开始处理...
                                    </div>
                                    <div id="detailStatus" class="text-muted small mt-1"></div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-outline-danger btn-sm" id="cancelBtn">
                                        <i class="fas fa-stop me-1"></i>
                                        取消处理
                                    </button>
                                </div>
                            </div>

                            <!-- 处理步骤指示器 -->
                            <div class="process-steps mt-4">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <div class="step-indicator" id="step1">
                                            <i class="fas fa-upload"></i>
                                            <small>文件上传</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="step-indicator" id="step2">
                                            <i class="fas fa-cog"></i>
                                            <small>音频处理</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="step-indicator" id="step3">
                                            <i class="fas fa-microphone"></i>
                                            <small>语音识别</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="step-indicator" id="step4">
                                            <i class="fas fa-sparkles"></i>
                                            <small>AI优化</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="card mt-4 animate__animated animate__fadeInUp" id="resultCard" style="display: none; animation-delay: 0.6s;">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                转换完成
                            </h5>
                            <small class="text-light" id="completionTime">完成时间: --</small>
                        </div>
                        <div class="card-body">
                            <!-- 结果统计 -->
                            <div class="row mb-4">
                                <div class="col-md-4 text-center">
                                    <div class="result-stat">
                                        <h4 class="text-primary mb-1" id="totalDuration">--</h4>
                                        <small class="text-muted">音频时长</small>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="result-stat">
                                        <h4 class="text-success mb-1" id="subtitleCount">--</h4>
                                        <small class="text-muted">字幕条数</small>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="result-stat">
                                        <h4 class="text-info mb-1" id="processingTime">--</h4>
                                        <small class="text-muted">处理时间</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 字幕预览 -->
                            <div class="subtitle-preview mb-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-eye me-2"></i>字幕预览
                                </h6>
                                <div class="preview-container" id="subtitlePreview">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                                        <p>字幕预览将在这里显示</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 下载按钮 -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-success btn-lg w-100" id="downloadOriginal">
                                        <i class="fas fa-download me-2"></i>
                                        下载原始字幕
                                        <small class="d-block">标准语音识别结果</small>
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-primary btn-lg w-100" id="downloadOptimized">
                                        <i class="fas fa-sparkles me-2"></i>
                                        下载AI优化字幕
                                        <small class="d-block">AI智能优化后</small>
                                    </button>
                                </div>
                            </div>

                            <!-- 重新处理按钮 -->
                            <div class="text-center mt-3">
                                <button class="btn btn-outline-secondary" id="processNewFile">
                                    <i class="fas fa-plus me-2"></i>
                                    处理新文件
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="bg-dark text-light mt-5 py-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-video me-2"></i>
                            {{ config.app_name }}
                        </h5>
                        <p class="text-muted">
                            智能音视频转字幕工具，让字幕制作变得简单高效。
                            支持多种格式，AI智能优化，一键生成高质量字幕。
                        </p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-3">功能特点</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                多格式支持
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                AI智能优化
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                高精度识别
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                标准SRT格式
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-3">技术支持</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-cloud text-info me-2"></i>
                                腾讯云语音识别
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-brain text-warning me-2"></i>
                                DeepSeek AI优化
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                安全可靠
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-rocket text-danger me-2"></i>
                                高效处理
                            </li>
                        </ul>
                    </div>
                </div>
                <hr class="my-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">
                            &copy; 2025 音视频转字幕工具 - 基于腾讯云语音识别 & DeepSeek AI
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            <i class="fas fa-code me-1"></i>
                            Made with ❤️ using Flask & Bootstrap
                        </small>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- 确认对话框模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle text-warning me-2"></i>
                        确认操作
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="confirmModalBody">
                    确认要执行此操作吗？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmModalBtn">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- 页面加载完成后隐藏加载遮罩 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }, 500);
        });
    </script>
</body>
</html>
