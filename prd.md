# 音视频转字幕网页应用开发 - AI提示词

## 项目概述
开发一个基于Python Flask的音视频转字幕网页应用，支持多种音视频格式上传、腾讯云语音识别、DeepSeek AI字幕优化等功能。

## 需求分析与优化

### 原需求完善补充：
1. **安全性增强**：文件类型验证、路径遍历防护、临时文件清理
2. **错误处理**：网络异常、API调用失败、文件损坏等情况处理
3. **用户体验**：进度条、状态提示、操作引导、响应式设计
4. **性能优化**：文件分片上传、异步处理、内存管理
5. **日志记录**：操作日志、错误日志、性能监控
6. **配置管理**：环境变量、配置文件、参数调优

---

## 步骤一：项目结构和基础配置

**提示词：**
```
请创建一个Python Flask音视频转字幕应用的完整项目结构。要求：

1. 创建标准的Flask项目目录结构，包含：
   - app.py（主应用文件）
   - requirements.txt（依赖包列表）
   - config.py（配置文件）
   - static/（静态资源目录：CSS、JS、图片）
   - templates/（HTML模板目录）
   - uploads/（上传文件存储目录）
   - temp/（临时文件目录）
   - logs/（日志文件目录）

2. 在config.py中配置：
   - Flask基础配置（SECRET_KEY、上传路径等）
   - 文件上传限制（500MB、支持格式）
   - API密钥配置：
     ```python
     DEEPSEEK_API_KEY = "***********************************"
     TENCENT_SECRET_ID = "AKIDeMsBIDw51lIiwynQq6KYkpkU0KQK3e1B"
     TENCENT_SECRET_KEY = "2qTt9D8qMgqqdDapw471nQtDMK9qNly9"
     ```
   - 支持的文件格式列表
   - 日志配置

3. requirements.txt包含所需依赖：
   - Flask及相关扩展
   - moviepy（视频处理）
   - tencentcloud-sdk-python（腾讯云SDK）
   - requests（HTTP请求）
   - 其他必要依赖

4. 创建基础的app.py框架，包含：
   - Flask应用初始化
   - 配置加载
   - 日志设置
   - 基础路由结构
   - 错误处理装饰器

请确保项目结构清晰、配置完整、易于维护。
```

---

## 步骤二：前端界面开发

**提示词：**
```
请为音视频转字幕应用创建完整的前端界面。要求：

1. 创建主页面模板（templates/index.html）：
   - 响应式设计，兼容PC和移动端
   - 使用Bootstrap 5框架
   - 包含文件上传区域（拖拽上传支持）
   - 进度条显示区域
   - 结果展示区域
   - 下载按钮区域

2. 创建样式文件（static/css/style.css）：
   - 现代化UI设计
   - 文件上传区域样式
   - 进度条动画效果
   - 状态提示样式
   - 响应式布局

3. 创建JavaScript文件（static/js/main.js）：
   - 文件上传功能（支持拖拽、点击选择）
   - 文件格式和大小验证
   - 上传进度显示
   - AJAX请求处理
   - 实时状态更新
   - 确认弹窗实现
   - 下载功能

4. 界面功能要求：
   - 文件格式提示（MP3、WAV、M4A、AAC、MP4、AVI、MOV、MKV）
   - 文件大小限制提示（500MB）
   - 上传成功确认弹窗
   - 转换进度实时显示
   - 错误信息友好提示
   - AI优化选项开关

5. 用户体验优化：
   - 加载动画
   - 操作引导提示
   - 键盘快捷键支持
   - 无障碍访问支持

请确保界面美观、交互流畅、用户体验良好。
```

---

## 步骤三：文件上传和处理模块

**提示词：**
```
请实现音视频文件上传和处理功能。要求：

1. 在app.py中创建文件上传路由：
   - /upload POST路由处理文件上传
   - 文件类型验证（仅允许指定格式）
   - 文件大小验证（不超过500MB）
   - 安全文件名处理（防止路径遍历攻击）
   - 文件保存到uploads目录
   - 返回上传状态和文件信息

2. 创建文件处理工具函数：
   - 音频格式检测和验证
   - 视频文件音频轨道提取（使用moviepy）
   - 音频格式转换为WAV（适合语音识别）
   - 文件元信息获取（时长、格式、大小等）
   - 临时文件管理和清理

3. 安全性措施：
   - 文件类型白名单验证
   - 文件内容MIME类型检查
   - 上传文件重命名（UUID+时间戳）
   - 路径遍历攻击防护
   - 文件大小二次验证

4. 错误处理：
   - 文件格式不支持
   - 文件损坏或无法读取
   - 磁盘空间不足
   - 网络传输中断
   - 音频提取失败

5. 性能优化：
   - 大文件分片处理
   - 内存使用优化
   - 临时文件及时清理
   - 异步处理支持

6. 创建进度查询路由：
   - /progress/<task_id> GET路由
   - 返回处理进度和状态
   - 支持实时进度更新

请确保文件处理安全、高效、稳定。
```

---

## 步骤四：腾讯云语音识别集成

**提示词：**
```
请实现腾讯云语音识别功能，将音频转换为SRT字幕。要求：

1. 创建腾讯云语音识别服务类：
   - 使用提供的SecretId和SecretKey
   - 支持长音频识别（无时长限制）
   - 自动音频分片处理（适应API限制）
   - 识别结果合并和时间轴对齐

2. 实现语音识别核心功能：
   - 音频预处理（格式转换、采样率调整）
   - 分片上传和识别
   - 识别结果获取和解析
   - 时间戳精确计算
   - SRT格式字幕生成

3. 创建转换路由：
   - /convert POST路由启动转换
   - 异步任务处理（避免请求超时）
   - 进度状态更新
   - 结果文件生成

4. SRT字幕格式要求：
   - 标准SRT格式（序号、时间轴、文本）
   - 中文简体输出
   - 合理的字幕分段（每段3-5秒）
   - 时间轴精确对齐

5. 错误处理和重试机制：
   - API调用失败重试
   - 网络超时处理
   - 识别结果为空处理
   - 音频质量过低提示

6. 性能优化：
   - 并发识别处理
   - 结果缓存机制
   - 内存使用优化
   - 识别速度优化

7. 创建状态查询功能：
   - 实时进度反馈
   - 错误状态通知
   - 完成状态确认

请确保语音识别准确、高效、稳定。
```

---

## 步骤五：DeepSeek AI字幕优化

**提示词：**
```
请实现DeepSeek AI字幕优化功能。要求：

1. 创建DeepSeek API服务类：
   - 使用提供的API密钥
   - 实现字幕文本优化请求
   - 处理API响应和错误
   - 支持批量文本处理

2. 字幕优化功能：
   - 语言润色和语法优化
   - 句子重组和逻辑整理
   - 标点符号规范化
   - 语言表达自然化
   - 保持原意不变

3. 创建优化路由：
   - /optimize POST路由处理优化请求
   - 接收原始SRT文件
   - 返回优化后的SRT文件
   - 支持批量段落优化

4. 优化策略：
   - 保持SRT时间轴不变
   - 优化文本内容质量
   - 处理识别错误和口语化表达
   - 统一术语和表达方式

5. 提示词设计：
   - 专业的字幕优化提示词
   - 保持时间轴格式
   - 中文语言规范
   - 上下文连贯性

6. 错误处理：
   - API调用失败处理
   - 优化结果验证
   - 原文件备份保护
   - 网络异常重试

7. 用户选择功能：
   - 提供原始和优化两个版本
   - 用户可选择下载版本
   - 对比显示功能

请确保AI优化效果好、安全可靠。
```

---

## 步骤六：下载功能和文件管理

**提示词：**
```
请实现字幕文件下载功能和完整的文件管理系统。要求：

1. 创建下载路由：
   - /download/<file_id> GET路由
   - 支持原始字幕和优化字幕下载
   - 文件名格式：原文件名_字幕.srt
   - 安全的文件访问控制

2. 文件管理功能：
   - 临时文件定期清理（24小时后删除）
   - 上传文件管理和清理
   - 生成文件的版本控制
   - 磁盘空间监控

3. 下载安全性：
   - 文件访问权限验证
   - 防止路径遍历攻击
   - 文件存在性检查
   - 下载次数限制

4. 用户界面集成：
   - 下载按钮状态管理
   - 下载进度显示
   - 多版本文件选择
   - 下载历史记录

5. 文件清理策略：
   - 自动清理过期文件
   - 手动清理接口
   - 存储空间优化
   - 日志记录清理操作

6. 错误处理：
   - 文件不存在处理
   - 下载中断恢复
   - 权限不足提示
   - 网络异常处理

请确保下载功能安全、稳定、用户友好。
```

---

## 步骤七：系统集成和优化

**提示词：**
```
请完成系统集成、错误处理和性能优化。要求：

1. 完善错误处理系统：
   - 全局异常处理器
   - 友好的错误页面
   - 详细的错误日志记录
   - 用户友好的错误提示

2. 日志系统：
   - 操作日志记录
   - 错误日志记录
   - 性能监控日志
   - 日志轮转和清理

3. 性能优化：
   - 异步任务处理
   - 内存使用优化
   - 文件I/O优化
   - 网络请求优化

4. 安全性加强：
   - CSRF保护
   - 文件上传安全
   - API调用安全
   - 敏感信息保护

5. 用户体验优化：
   - 加载状态提示
   - 操作反馈
   - 进度显示优化
   - 响应式设计完善

6. 系统监控：
   - 健康检查接口
   - 系统状态监控
   - 资源使用监控
   - 性能指标收集

7. 部署准备：
   - 启动脚本
   - 环境检查
   - 依赖安装脚本
   - 配置验证

8. 文档和说明：
   - README.md文件
   - 安装部署指南
   - 使用说明文档
   - 故障排除指南

请确保系统稳定、安全、易于部署和维护。
```

---

## 步骤八：测试和部署

**提示词：**
```
请创建测试用例和部署脚本，确保应用可以正常运行。要求：

1. 创建测试文件：
   - 单元测试（文件处理、API调用等）
   - 集成测试（完整流程测试）
   - 性能测试（大文件处理）
   - 安全测试（文件上传安全）

2. 部署脚本：
   - install.sh（依赖安装脚本）
   - start.sh（应用启动脚本）
   - stop.sh（应用停止脚本）
   - 环境检查脚本

3. 配置文件：
   - 生产环境配置
   - 开发环境配置
   - 日志配置文件
   - 系统服务配置

4. 文档完善：
   - 详细的README.md
   - API文档
   - 故障排除指南
   - 常见问题解答

5. 运行验证：
   - 功能完整性检查
   - 性能基准测试
   - 错误处理验证
   - 安全性检查

6. 优化建议：
   - 系统资源配置建议
   - 性能调优参数
   - 监控和维护建议
   - 升级和扩展方案

请确保应用可以一键部署、稳定运行、易于维护。
```

---

## 总结

这份AI提示词涵盖了音视频转字幕应用的完整开发流程，包括：

1. **项目结构**：标准Flask项目组织
2. **前端界面**：现代化响应式设计
3. **文件处理**：安全高效的音视频处理
4. **语音识别**：腾讯云API集成
5. **AI优化**：DeepSeek字幕优化
6. **下载管理**：完整的文件管理系统
7. **系统集成**：错误处理和性能优化
8. **测试部署**：完整的部署和测试方案

每个步骤都可以独立提交给AI Agent执行，确保代码质量和功能完整性。整个应用将具备企业级的安全性、稳定性和用户体验。
