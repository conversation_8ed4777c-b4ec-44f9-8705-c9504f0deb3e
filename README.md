# 音视频转字幕网页应用

一个基于Python Flask的智能音视频转字幕工具，支持多种音视频格式，使用腾讯云语音识别技术，配合DeepSeek AI进行字幕优化。

## 功能特点

- 🎵 **多格式支持**：支持MP3、WAV、M4A、AAC、MP4、AVI、MOV、MKV等主流音视频格式
- 🎯 **智能识别**：基于腾讯云语音识别技术，准确率高
- 🤖 **AI优化**：集成DeepSeek AI，自动优化字幕文本质量
- 📱 **响应式设计**：支持PC和移动端访问
- 🔒 **安全可靠**：完善的文件验证和安全措施
- ⚡ **高效处理**：支持大文件处理和实时进度显示

## 项目结构

```
yinpingandshiping to word/
├── app.py                 # 主应用文件
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── start.py              # 启动脚本
├── README.md             # 项目说明
├── static/               # 静态资源
│   ├── css/
│   │   └── style.css     # 样式文件
│   ├── js/
│   │   └── main.js       # JavaScript文件
│   └── images/           # 图片资源
├── templates/            # HTML模板
│   ├── index.html        # 主页模板
│   └── error.html        # 错误页面模板
├── uploads/              # 上传文件存储
├── temp/                 # 临时文件
└── logs/                 # 日志文件
```

## 快速开始

### 环境要求

- Python 3.8+
- Windows/Linux/macOS
- 网络连接（用于API调用）

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果使用Git
   git clone <repository-url>
   cd "yinpingandshiping to word"
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动应用**
   ```bash
   python start.py
   ```

4. **访问应用**
   - 打开浏览器访问：http://localhost:5000
   - 局域网访问：http://你的IP:5000

### 使用方法

1. **上传文件**
   - 点击"选择文件"按钮或直接拖拽文件到上传区域
   - 支持最大500MB的音视频文件

2. **等待处理**
   - 系统会自动进行语音识别和字幕生成
   - 实时显示处理进度

3. **下载字幕**
   - 处理完成后可下载原始字幕或AI优化字幕
   - 字幕格式为标准SRT格式

## 配置说明

### API密钥配置

在 `config.py` 中配置以下API密钥：

```python
# DeepSeek AI API
DEEPSEEK_API_KEY = "your-deepseek-api-key"

# 腾讯云API
TENCENT_SECRET_ID = "your-tencent-secret-id"
TENCENT_SECRET_KEY = "your-tencent-secret-key"
```

### 环境变量（生产环境推荐）

```bash
export FLASK_ENV=production
export SECRET_KEY=your-secret-key
export DEEPSEEK_API_KEY=your-deepseek-api-key
export TENCENT_SECRET_ID=your-tencent-secret-id
export TENCENT_SECRET_KEY=your-tencent-secret-key
```

## 开发计划

- [x] 步骤一：项目结构和基础配置
- [ ] 步骤二：前端界面开发
- [ ] 步骤三：文件上传和处理模块
- [ ] 步骤四：腾讯云语音识别集成
- [ ] 步骤五：DeepSeek AI字幕优化
- [ ] 步骤六：下载功能和文件管理
- [ ] 步骤七：系统集成和优化
- [ ] 步骤八：测试和部署

## 技术栈

- **后端**：Python Flask
- **前端**：HTML5 + CSS3 + JavaScript + Bootstrap 5
- **音视频处理**：MoviePy + FFmpeg
- **语音识别**：腾讯云语音识别API
- **AI优化**：DeepSeek AI API
- **文件处理**：Werkzeug + python-magic

## 注意事项

1. **网络要求**：需要稳定的网络连接用于API调用
2. **文件大小**：单个文件最大支持500MB
3. **处理时间**：处理时间取决于文件大小和网络状况
4. **存储空间**：确保有足够的磁盘空间存储临时文件
5. **API配额**：注意API调用次数和配额限制

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保Python版本为3.8+
   - 使用pip升级：`pip install --upgrade pip`
   - 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

2. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过500MB
   - 检查磁盘空间是否充足

3. **API调用失败**
   - 验证API密钥是否正确
   - 检查网络连接
   - 确认API配额是否充足

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/your-username/your-repo
