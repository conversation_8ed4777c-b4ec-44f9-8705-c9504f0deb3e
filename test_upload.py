# -*- coding: utf-8 -*-
"""
文件上传和处理功能测试脚本
测试文件上传、验证、处理等功能
"""

import os
import sys
import time
import tempfile
import json
from io import BytesIO

def test_file_processing_modules():
    """测试文件处理模块"""
    print("📋 文件处理模块测试")
    print("-" * 30)
    
    try:
        # 测试音频处理器导入
        from audio_processor import AudioProcessor, process_media_file
        print("✓ 音频处理器模块导入成功")
        
        # 测试任务处理器导入
        from task_processor import TaskProcessor
        print("✓ 任务处理器模块导入成功")
        
        # 测试音频处理器初始化
        processor = AudioProcessor()
        print("✓ 音频处理器初始化成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 模块测试失败: {e}")
        return False

def test_file_upload_routes():
    """测试文件上传路由"""
    print("\n📋 文件上传路由测试")
    print("-" * 30)
    
    try:
        from app import app
        
        with app.test_client() as client:
            # 测试上传接口（无文件）
            response = client.post('/upload')
            print(f"✓ 无文件上传测试: {response.status_code}")
            
            if response.status_code == 400:
                data = json.loads(response.get_data(as_text=True))
                if not data['success'] and '没有选择文件' in data['error']:
                    print("  ✓ 错误处理正确")
                else:
                    print("  ⚠️ 错误消息可能不正确")
            
            # 测试配置接口
            response = client.get('/api/config')
            if response.status_code == 200:
                print("✓ 配置接口正常")
                
                config_data = json.loads(response.get_data(as_text=True))
                if 'allowed_extensions' in config_data:
                    print(f"  ✓ 支持的扩展名: {len(config_data['allowed_extensions'])}种")
                else:
                    print("  ⚠️ 配置数据不完整")
            else:
                print(f"❌ 配置接口异常: {response.status_code}")
            
            # 测试进度查询接口（不存在的任务）
            response = client.get('/progress/nonexistent-task')
            if response.status_code == 404:
                print("✓ 不存在任务的进度查询处理正确")
            else:
                print(f"⚠️ 不存在任务的进度查询响应: {response.status_code}")
            
            # 测试任务列表接口
            response = client.get('/tasks')
            if response.status_code == 200:
                print("✓ 任务列表接口正常")
            else:
                print(f"❌ 任务列表接口异常: {response.status_code}")
            
            # 测试清理接口
            response = client.get('/cleanup')
            if response.status_code == 200:
                print("✓ 清理接口正常")
            else:
                print(f"❌ 清理接口异常: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路由测试失败: {e}")
        return False

def test_file_validation():
    """测试文件验证功能"""
    print("\n📋 文件验证功能测试")
    print("-" * 30)
    
    try:
        from app import app, allowed_file, validate_file_content, generate_unique_filename
        
        # 测试文件名验证
        test_files = [
            ('test.mp3', True),
            ('test.wav', True),
            ('test.mp4', True),
            ('test.txt', False),
            ('test.exe', False),
            ('test', False),
        ]
        
        for filename, expected in test_files:
            result = allowed_file(filename)
            if result == expected:
                print(f"✓ 文件名验证正确: {filename} -> {result}")
            else:
                print(f"❌ 文件名验证错误: {filename} -> {result}, 期望: {expected}")
        
        # 测试唯一文件名生成
        original_name = "test_audio.mp3"
        unique_name = generate_unique_filename(original_name)
        
        if unique_name != original_name and unique_name.endswith('.mp3'):
            print(f"✓ 唯一文件名生成正确: {original_name} -> {unique_name}")
        else:
            print(f"❌ 唯一文件名生成错误: {unique_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件验证测试失败: {e}")
        return False

def test_task_management():
    """测试任务管理功能"""
    print("\n📋 任务管理功能测试")
    print("-" * 30)
    
    try:
        from app import app, create_task, get_task, update_task
        
        # 创建测试任务
        file_info = {
            'original_name': 'test.mp3',
            'unique_name': 'test_123.mp3',
            'file_path': '/tmp/test_123.mp3',
            'size': 1024000,
            'file_type': 'audio'
        }
        
        task_id = create_task('test_task', file_info)
        
        if task_id:
            print(f"✓ 任务创建成功: {task_id}")
            
            # 获取任务
            task = get_task(task_id)
            if task and task['id'] == task_id:
                print("✓ 任务获取成功")
                
                # 更新任务
                if update_task(task_id, status='processing', progress=50):
                    print("✓ 任务更新成功")
                    
                    # 验证更新
                    updated_task = get_task(task_id)
                    if updated_task['status'] == 'processing' and updated_task['progress'] == 50:
                        print("✓ 任务更新验证成功")
                    else:
                        print("❌ 任务更新验证失败")
                else:
                    print("❌ 任务更新失败")
            else:
                print("❌ 任务获取失败")
        else:
            print("❌ 任务创建失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n📋 错误处理测试")
    print("-" * 30)
    
    try:
        from app import app
        
        with app.test_client() as client:
            # 测试404错误
            response = client.get('/nonexistent-route')
            if response.status_code == 404:
                print("✓ 404错误处理正确")
            else:
                print(f"⚠️ 404错误处理响应: {response.status_code}")
            
            # 测试取消不存在的任务
            response = client.post('/cancel/nonexistent-task')
            if response.status_code == 404:
                print("✓ 取消不存在任务的错误处理正确")
            else:
                print(f"⚠️ 取消不存在任务的响应: {response.status_code}")
            
            # 测试处理不存在的任务
            response = client.post('/process/nonexistent-task')
            if response.status_code == 404:
                print("✓ 处理不存在任务的错误处理正确")
            else:
                print(f"⚠️ 处理不存在任务的响应: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def test_security_features():
    """测试安全功能"""
    print("\n📋 安全功能测试")
    print("-" * 30)
    
    try:
        from app import app, generate_unique_filename
        from werkzeug.utils import secure_filename
        
        # 测试安全文件名
        dangerous_names = [
            '../../../etc/passwd',
            '..\\..\\windows\\system32\\config\\sam',
            'test<script>alert(1)</script>.mp3',
            'test"file".mp3'
        ]
        
        for dangerous_name in dangerous_names:
            safe_name = generate_unique_filename(dangerous_name)
            if '../' not in safe_name and '..\\' not in safe_name and '<script>' not in safe_name:
                print(f"✓ 危险文件名处理正确: {dangerous_name[:20]}...")
            else:
                print(f"❌ 危险文件名处理失败: {safe_name}")
        
        # 测试文件大小限制
        with app.test_client() as client:
            # 模拟大文件上传（这里只是测试响应，不实际上传大文件）
            large_data = b'x' * (10 * 1024 * 1024)  # 10MB数据
            
            response = client.post('/upload', data={
                'file': (BytesIO(large_data), 'large_file.mp3')
            })
            
            # 应该返回413或400错误
            if response.status_code in [400, 413]:
                print("✓ 文件大小限制处理正确")
            else:
                print(f"⚠️ 文件大小限制响应: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("音视频转字幕应用 - 文件上传和处理功能测试")
    print("=" * 50)
    
    tests = [
        ("文件处理模块测试", test_file_processing_modules),
        ("文件上传路由测试", test_file_upload_routes),
        ("文件验证功能测试", test_file_validation),
        ("任务管理功能测试", test_task_management),
        ("错误处理测试", test_error_handling),
        ("安全功能测试", test_security_features),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常：{e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有文件上传和处理功能测试通过！")
        print("📝 步骤三：文件上传和处理模块 - 完成")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
