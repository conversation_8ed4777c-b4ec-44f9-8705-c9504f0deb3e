# -*- coding: utf-8 -*-
"""
腾讯云语音识别服务模块
负责音频文件的语音识别和SRT字幕生成
"""

import os
import json
import time
import base64
import logging
from datetime import datetime, timedelta

try:
    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.asr.v20190614 import asr_client, models
    from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
    TENCENT_SDK_AVAILABLE = True
except ImportError:
    TENCENT_SDK_AVAILABLE = False
    logging.warning("腾讯云SDK未安装，语音识别功能不可用")

class TencentASR:
    """腾讯云语音识别服务"""
    
    def __init__(self, secret_id, secret_key, region="ap-beijing"):
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.region = region
        self.logger = logging.getLogger(__name__)
        
        if not TENCENT_SDK_AVAILABLE:
            raise Exception("腾讯云SDK未安装，请安装tencentcloud-sdk-python")
        
        # 初始化客户端
        self._init_client()
    
    def _init_client(self):
        """初始化腾讯云客户端"""
        try:
            # 实例化一个认证对象
            cred = credential.Credential(self.secret_id, self.secret_key)
            
            # 实例化一个http选项
            httpProfile = HttpProfile()
            httpProfile.endpoint = "asr.tencentcloudapi.com"
            
            # 实例化一个client选项
            clientProfile = ClientProfile()
            clientProfile.httpProfile = httpProfile
            
            # 实例化要请求产品的client对象
            self.client = asr_client.AsrClient(cred, self.region, clientProfile)
            
            self.logger.info("腾讯云ASR客户端初始化成功")
            
        except Exception as e:
            self.logger.error(f"腾讯云ASR客户端初始化失败: {str(e)}")
            raise
    
    def recognize_audio_file(self, audio_path, callback=None):
        """识别音频文件"""
        try:
            self.logger.info(f"开始识别音频文件: {audio_path}")
            
            # 检查文件是否存在
            if not os.path.exists(audio_path):
                raise Exception(f"音频文件不存在: {audio_path}")
            
            # 获取文件大小
            file_size = os.path.getsize(audio_path)
            self.logger.info(f"音频文件大小: {file_size} bytes")
            
            # 根据文件大小选择识别方式
            if file_size <= 5 * 1024 * 1024:  # 5MB以下使用一句话识别
                return self._recognize_short_audio(audio_path, callback)
            else:  # 大文件使用录音文件识别
                return self._recognize_long_audio(audio_path, callback)
                
        except Exception as e:
            self.logger.error(f"音频识别失败: {str(e)}")
            raise
    
    def _recognize_short_audio(self, audio_path, callback=None):
        """识别短音频（一句话识别）"""
        try:
            # 读取音频文件
            with open(audio_path, 'rb') as f:
                audio_data = f.read()
            
            # Base64编码
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            # 实例化一个请求对象
            req = models.SentenceRecognitionRequest()
            
            # 设置请求参数
            params = {
                "ProjectId": 0,
                "SubServiceType": 2,
                "EngSerViceType": "16k_zh",
                "SourceType": 1,
                "VoiceFormat": "wav",
                "UsrAudioKey": os.path.basename(audio_path),
                "Data": audio_base64
            }
            req.from_json_string(json.dumps(params))
            
            if callback:
                callback("正在进行语音识别...")
            
            # 发起请求
            resp = self.client.SentenceRecognition(req)
            
            # 解析结果
            result = json.loads(resp.to_json_string())
            
            if result.get('Result'):
                recognition_text = result['Result']
                self.logger.info(f"短音频识别成功: {recognition_text[:50]}...")
                
                # 生成简单的SRT格式
                srt_content = self._create_simple_srt(recognition_text)
                
                return {
                    'success': True,
                    'text': recognition_text,
                    'srt_content': srt_content,
                    'method': 'short_audio'
                }
            else:
                raise Exception("识别结果为空")
                
        except TencentCloudSDKException as e:
            self.logger.error(f"腾讯云SDK异常: {e}")
            raise Exception(f"语音识别失败: {e}")
        except Exception as e:
            self.logger.error(f"短音频识别失败: {str(e)}")
            raise
    
    def _recognize_long_audio(self, audio_path, callback=None):
        """识别长音频（录音文件识别）"""
        try:
            # 读取音频文件
            with open(audio_path, 'rb') as f:
                audio_data = f.read()
            
            # Base64编码
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            # 实例化一个请求对象
            req = models.CreateRecTaskRequest()
            
            # 设置请求参数
            params = {
                "EngineModelType": "16k_zh",
                "ChannelNum": 1,
                "ResTextFormat": 0,
                "SourceType": 1,
                "Data": audio_base64
            }
            req.from_json_string(json.dumps(params))
            
            if callback:
                callback("提交长音频识别任务...")
            
            # 提交识别任务
            resp = self.client.CreateRecTask(req)
            result = json.loads(resp.to_json_string())
            
            if not result.get('Data', {}).get('TaskId'):
                raise Exception("提交识别任务失败")
            
            task_id = result['Data']['TaskId']
            self.logger.info(f"长音频识别任务ID: {task_id}")
            
            # 轮询获取结果
            return self._poll_recognition_result(task_id, callback)
            
        except TencentCloudSDKException as e:
            self.logger.error(f"腾讯云SDK异常: {e}")
            raise Exception(f"语音识别失败: {e}")
        except Exception as e:
            self.logger.error(f"长音频识别失败: {str(e)}")
            raise
    
    def _poll_recognition_result(self, task_id, callback=None, max_wait_time=1800):
        """轮询获取识别结果"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                # 实例化一个请求对象
                req = models.DescribeTaskStatusRequest()
                params = {"TaskId": task_id}
                req.from_json_string(json.dumps(params))
                
                # 查询任务状态
                resp = self.client.DescribeTaskStatus(req)
                result = json.loads(resp.to_json_string())
                
                data = result.get('Data', {})
                status = data.get('StatusStr', '')
                
                self.logger.debug(f"任务状态: {status}")
                
                if callback:
                    elapsed = int(time.time() - start_time)
                    callback(f"语音识别进行中... ({elapsed}秒)")
                
                if status == 'success':
                    # 识别成功
                    result_detail = data.get('ResultDetail', [])
                    if result_detail:
                        # 解析识别结果
                        recognition_result = self._parse_recognition_result(result_detail)
                        
                        self.logger.info(f"长音频识别成功，共{len(result_detail)}个片段")
                        
                        return {
                            'success': True,
                            'text': recognition_result['text'],
                            'srt_content': recognition_result['srt_content'],
                            'segments': result_detail,
                            'method': 'long_audio'
                        }
                    else:
                        raise Exception("识别结果为空")
                        
                elif status == 'failed':
                    error_msg = data.get('ErrorMsg', '识别失败')
                    raise Exception(f"识别失败: {error_msg}")
                
                # 等待一段时间后再次查询
                time.sleep(3)
            
            raise Exception("识别超时")
            
        except Exception as e:
            self.logger.error(f"轮询识别结果失败: {str(e)}")
            raise
    
    def _parse_recognition_result(self, result_detail):
        """解析识别结果并生成SRT"""
        try:
            full_text = ""
            srt_entries = []
            
            for i, segment in enumerate(result_detail):
                text = segment.get('FinalSentence', '').strip()
                start_time = segment.get('StartMs', 0) / 1000.0  # 转换为秒
                end_time = segment.get('EndMs', 0) / 1000.0
                
                if text:
                    full_text += text + " "
                    
                    # 生成SRT条目
                    srt_entry = self._create_srt_entry(
                        i + 1, 
                        start_time, 
                        end_time, 
                        text
                    )
                    srt_entries.append(srt_entry)
            
            srt_content = "\n\n".join(srt_entries)
            
            return {
                'text': full_text.strip(),
                'srt_content': srt_content
            }
            
        except Exception as e:
            self.logger.error(f"解析识别结果失败: {str(e)}")
            raise
    
    def _create_simple_srt(self, text, duration=None):
        """为短音频创建简单的SRT"""
        try:
            # 如果没有提供时长，默认按字数估算
            if duration is None:
                # 假设每秒3个字符
                duration = max(len(text) / 3, 2)
            
            return self._create_srt_entry(1, 0, duration, text)
            
        except Exception as e:
            self.logger.error(f"创建简单SRT失败: {str(e)}")
            raise
    
    def _create_srt_entry(self, index, start_time, end_time, text):
        """创建SRT条目"""
        try:
            # 格式化时间
            start_formatted = self._format_srt_time(start_time)
            end_formatted = self._format_srt_time(end_time)
            
            # 创建SRT条目
            srt_entry = f"{index}\n{start_formatted} --> {end_formatted}\n{text}"
            
            return srt_entry
            
        except Exception as e:
            self.logger.error(f"创建SRT条目失败: {str(e)}")
            raise
    
    def _format_srt_time(self, seconds):
        """格式化SRT时间格式"""
        try:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            milliseconds = int((seconds % 1) * 1000)
            
            return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
            
        except Exception as e:
            self.logger.error(f"格式化SRT时间失败: {str(e)}")
            return "00:00:00,000"
    
    def save_srt_file(self, srt_content, output_path):
        """保存SRT文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            self.logger.info(f"SRT文件保存成功: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"保存SRT文件失败: {str(e)}")
            raise

def create_asr_service(config):
    """创建ASR服务实例"""
    try:
        secret_id = config.get('TENCENT_SECRET_ID')
        secret_key = config.get('TENCENT_SECRET_KEY')
        region = config.get('TENCENT_REGION', 'ap-beijing')
        
        if not secret_id or not secret_key:
            raise Exception("腾讯云API密钥未配置")
        
        return TencentASR(secret_id, secret_key, region)
        
    except Exception as e:
        logging.error(f"创建ASR服务失败: {str(e)}")
        raise
