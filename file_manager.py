# -*- coding: utf-8 -*-
"""
文件管理模块
负责文件的存储、下载、清理和安全管理
"""

import os
import shutil
import logging
import hashlib
import mimetypes
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class FileManager:
    """文件管理器"""
    
    def __init__(self, upload_dir: str, temp_dir: str, log_dir: str):
        self.upload_dir = Path(upload_dir)
        self.temp_dir = Path(temp_dir)
        self.log_dir = Path(log_dir)
        self.logger = logging.getLogger(__name__)
        
        # 确保目录存在
        self._ensure_directories()
        
        # 支持的文件类型
        self.allowed_extensions = {
            'audio': {'.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg', '.wma'},
            'video': {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.3gp'},
            'subtitle': {'.srt', '.vtt', '.ass', '.ssa'}
        }
        
        self.logger.info("文件管理器初始化完成")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.upload_dir, self.temp_dir, self.log_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"确保目录存在: {directory}")
    
    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """获取文件详细信息"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return None
            
            stat = path.stat()
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(file_path)
            
            # 获取MIME类型
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # 判断文件类型
            file_type = self._get_file_type(path.suffix.lower())
            
            info = {
                'path': str(path),
                'name': path.name,
                'stem': path.stem,
                'suffix': path.suffix,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / 1024 / 1024, 2),
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'accessed_time': datetime.fromtimestamp(stat.st_atime),
                'mime_type': mime_type,
                'file_type': file_type,
                'hash': file_hash,
                'is_readable': os.access(file_path, os.R_OK),
                'is_writable': os.access(file_path, os.W_OK)
            }
            
            return info
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败 {file_path}: {str(e)}")
            return None
    
    def _calculate_file_hash(self, file_path: str, algorithm: str = 'md5') -> str:
        """计算文件哈希值"""
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算文件哈希失败 {file_path}: {str(e)}")
            return ""
    
    def _get_file_type(self, extension: str) -> str:
        """根据扩展名判断文件类型"""
        extension = extension.lower()
        
        for file_type, extensions in self.allowed_extensions.items():
            if extension in extensions:
                return file_type
        
        return 'unknown'
    
    def validate_file_security(self, file_path: str) -> Tuple[bool, str]:
        """验证文件安全性"""
        try:
            path = Path(file_path)
            
            # 检查文件是否存在
            if not path.exists():
                return False, "文件不存在"
            
            # 检查文件大小
            if path.stat().st_size == 0:
                return False, "文件大小为0"
            
            # 检查文件扩展名
            all_extensions = set()
            for extensions in self.allowed_extensions.values():
                all_extensions.update(extensions)

            if path.suffix.lower() not in all_extensions:
                return False, f"不支持的文件类型: {path.suffix}"
            
            # 检查文件路径安全性
            if not self._is_safe_path(file_path):
                return False, "文件路径不安全"
            
            # 检查文件权限
            if not os.access(file_path, os.R_OK):
                return False, "文件不可读"
            
            return True, "文件验证通过"
            
        except Exception as e:
            self.logger.error(f"文件安全验证失败 {file_path}: {str(e)}")
            return False, f"验证失败: {str(e)}"
    
    def _is_safe_path(self, file_path: str) -> bool:
        """检查文件路径是否安全"""
        try:
            # 解析路径
            path = Path(file_path).resolve()
            
            # 检查是否在允许的目录内
            allowed_dirs = [self.upload_dir.resolve(), self.temp_dir.resolve()]
            
            for allowed_dir in allowed_dirs:
                try:
                    path.relative_to(allowed_dir)
                    return True
                except ValueError:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"路径安全检查失败 {file_path}: {str(e)}")
            return False
    
    def create_download_response_info(self, file_path: str, download_name: str = None) -> Dict:
        """创建下载响应信息"""
        try:
            file_info = self.get_file_info(file_path)
            if not file_info:
                raise Exception("无法获取文件信息")
            
            # 验证文件安全性
            is_safe, message = self.validate_file_security(file_path)
            if not is_safe:
                raise Exception(f"文件安全验证失败: {message}")
            
            # 确定下载文件名
            if not download_name:
                download_name = file_info['name']
            
            # 确保文件名安全
            safe_download_name = self._sanitize_filename(download_name)
            
            # 获取MIME类型
            mime_type = file_info['mime_type'] or 'application/octet-stream'
            
            response_info = {
                'file_path': file_path,
                'download_name': safe_download_name,
                'mime_type': mime_type,
                'file_size': file_info['size'],
                'file_hash': file_info['hash'],
                'last_modified': file_info['modified_time']
            }
            
            return response_info
            
        except Exception as e:
            self.logger.error(f"创建下载响应信息失败 {file_path}: {str(e)}")
            raise
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，确保安全"""
        import re
        
        # 移除或替换危险字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # 移除控制字符
        filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
        
        # 限制长度
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        # 确保不为空
        if not filename.strip():
            filename = 'download'
        
        return filename.strip()
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> Dict:
        """清理过期文件"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            cleanup_stats = {
                'upload_files_removed': 0,
                'temp_files_removed': 0,
                'total_size_freed': 0,
                'errors': []
            }
            
            # 清理上传目录
            cleanup_stats.update(self._cleanup_directory(
                self.upload_dir, cutoff_time, 'upload_files_removed'
            ))
            
            # 清理临时目录
            cleanup_stats.update(self._cleanup_directory(
                self.temp_dir, cutoff_time, 'temp_files_removed'
            ))
            
            self.logger.info(f"文件清理完成: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            self.logger.error(f"文件清理失败: {str(e)}")
            return {'error': str(e)}
    
    def _cleanup_directory(self, directory: Path, cutoff_time: datetime, counter_key: str) -> Dict:
        """清理指定目录"""
        stats = {counter_key: 0, 'total_size_freed': 0, 'errors': []}
        
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    try:
                        file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                        
                        if file_time < cutoff_time:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            
                            stats[counter_key] += 1
                            stats['total_size_freed'] += file_size
                            
                            self.logger.debug(f"删除过期文件: {file_path}")
                            
                    except Exception as e:
                        error_msg = f"删除文件失败 {file_path}: {str(e)}"
                        stats['errors'].append(error_msg)
                        self.logger.error(error_msg)
            
        except Exception as e:
            error_msg = f"清理目录失败 {directory}: {str(e)}"
            stats['errors'].append(error_msg)
            self.logger.error(error_msg)
        
        return stats
    
    def get_directory_stats(self) -> Dict:
        """获取目录统计信息"""
        try:
            stats = {}
            
            for name, directory in [
                ('upload', self.upload_dir),
                ('temp', self.temp_dir),
                ('log', self.log_dir)
            ]:
                dir_stats = self._get_single_directory_stats(directory)
                stats[name] = dir_stats
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取目录统计失败: {str(e)}")
            return {}
    
    def _get_single_directory_stats(self, directory: Path) -> Dict:
        """获取单个目录的统计信息"""
        try:
            if not directory.exists():
                return {
                    'exists': False,
                    'file_count': 0,
                    'total_size': 0,
                    'total_size_mb': 0
                }
            
            file_count = 0
            total_size = 0
            
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    file_count += 1
                    total_size += file_path.stat().st_size
            
            return {
                'exists': True,
                'file_count': file_count,
                'total_size': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2)
            }
            
        except Exception as e:
            self.logger.error(f"获取目录统计失败 {directory}: {str(e)}")
            return {
                'exists': False,
                'file_count': 0,
                'total_size': 0,
                'total_size_mb': 0,
                'error': str(e)
            }
    
    def move_file(self, src_path: str, dst_path: str) -> bool:
        """移动文件"""
        try:
            src = Path(src_path)
            dst = Path(dst_path)
            
            # 确保目标目录存在
            dst.parent.mkdir(parents=True, exist_ok=True)
            
            # 移动文件
            shutil.move(str(src), str(dst))
            
            self.logger.info(f"文件移动成功: {src} -> {dst}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件移动失败 {src_path} -> {dst_path}: {str(e)}")
            return False
    
    def copy_file(self, src_path: str, dst_path: str) -> bool:
        """复制文件"""
        try:
            src = Path(src_path)
            dst = Path(dst_path)
            
            # 确保目标目录存在
            dst.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(str(src), str(dst))
            
            self.logger.info(f"文件复制成功: {src} -> {dst}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件复制失败 {src_path} -> {dst_path}: {str(e)}")
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            path = Path(file_path)
            
            if path.exists() and path.is_file():
                path.unlink()
                self.logger.info(f"文件删除成功: {file_path}")
                return True
            else:
                self.logger.warning(f"文件不存在或不是文件: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"文件删除失败 {file_path}: {str(e)}")
            return False

def create_file_manager(config):
    """创建文件管理器实例"""
    try:
        upload_dir = config.get('UPLOAD_FOLDER')
        temp_dir = config.get('TEMP_FOLDER')
        log_dir = config.get('LOG_FOLDER')
        
        if not all([upload_dir, temp_dir, log_dir]):
            raise Exception("文件管理器配置不完整")
        
        return FileManager(upload_dir, temp_dir, log_dir)
        
    except Exception as e:
        logging.error(f"创建文件管理器失败: {str(e)}")
        raise
