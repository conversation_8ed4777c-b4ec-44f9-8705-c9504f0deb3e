/* 音视频转字幕应用样式文件 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    line-height: 1.6;
    min-height: 100vh;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-brand i {
    color: #ffc107;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

/* 功能特点样式 */
.feature-item {
    text-align: center;
    padding: 1.5rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-item i {
    transition: transform 0.3s ease;
}

.feature-item:hover i {
    transform: scale(1.2);
}

/* 上传区域样式 */
.upload-area {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 3px dashed #dee2e6 !important;
    border-radius: 20px;
    transition: all 0.4s ease;
    cursor: pointer;
    min-height: 250px;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.upload-area:hover::before {
    left: 100%;
}

.upload-area:hover {
    border-color: #007bff !important;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.2);
}

.upload-area.dragover {
    border-color: #28a745 !important;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(40, 167, 69, 0.3);
}

.upload-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
}

.upload-icon {
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
    color: #007bff !important;
    transform: scale(1.1);
}

.upload-area.dragover .upload-icon {
    color: #28a745 !important;
    transform: scale(1.2) rotate(10deg);
}

.upload-title {
    font-weight: 600;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.upload-area:hover .upload-title {
    color: #007bff;
}

/* 文件预览样式 */
.file-preview {
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    margin-top: 1rem;
}

.file-info-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

/* 上传选项样式 */
.upload-options {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.form-check-label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

/* 进度条样式 */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
    border-radius: 15px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.3) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0.3) 75%,
        transparent 75%,
        transparent
    );
    background-size: 30px 30px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 30px 30px;
    }
}

/* 状态文本样式 */
.status-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

/* 步骤指示器样式 */
.process-steps {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.step-indicator {
    padding: 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.step-indicator i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step-indicator small {
    display: block;
    color: #6c757d;
    font-weight: 500;
}

.step-indicator.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transform: scale(1.05);
}

.step-indicator.active i {
    color: white;
    animation: bounce 1s infinite;
}

.step-indicator.active small {
    color: white;
}

.step-indicator.completed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.step-indicator.completed i {
    color: white;
}

.step-indicator.completed small {
    color: white;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* 使用说明样式 */
.usage-step {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.usage-step:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 50%;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* 结果统计样式 */
.result-stat {
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.result-stat:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* 字幕预览样式 */
.subtitle-preview {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
}

.preview-container {
    max-height: 250px;
    overflow-y: auto;
    background: white;
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.preview-container::-webkit-scrollbar {
    width: 6px;
}

.preview-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 字幕条目样式 */
.subtitle-entry {
    padding: 0.75rem;
    border-left: 3px solid #007bff;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 0 8px 8px 0;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.subtitle-entry:hover {
    background: rgba(0, 123, 255, 0.1);
    transform: translateX(3px);
}

.subtitle-time {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.subtitle-text {
    font-size: 0.95rem;
    line-height: 1.4;
    color: #495057;
}

/* AI优化信息样式 */
.optimization-info .alert {
    border-radius: 10px;
    border: none;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

/* 字幕对比样式 */
.subtitle-comparison {
    background: white;
    border-radius: 10px;
    padding: 1rem;
}

.comparison-tabs .nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.comparison-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.comparison-tabs .nav-link:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.comparison-tabs .nav-link.active {
    background: #007bff;
    color: white;
    border-bottom: 2px solid #007bff;
}

.comparison-entry {
    padding: 1rem;
    border-radius: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.comparison-entry.has-changes {
    background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
    border-left: 4px solid #28a745;
}

.comparison-entry:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.original-text {
    padding: 0.75rem;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 8px;
    border-left: 3px solid #6c757d;
}

.optimized-text {
    padding: 0.75rem;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 8px;
    border-left: 3px solid #28a745;
}

.optimized-text .subtitle-text.text-success {
    font-weight: 500;
}

/* 预览标题样式 */
.subtitle-preview-header {
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

/* 文件统计样式 */
.stats-card {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.stats-card h6 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.stats-card p {
    font-size: 0.9rem;
    color: #6c757d;
}

.stats-card strong {
    color: #495057;
    font-weight: 600;
}

/* 文件管理按钮样式 */
.btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
}

.btn-outline-warning:hover {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border-color: #ffc107;
}

/* 打包下载按钮样式 */
.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

/* 进度条增强样式 */
.progress {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 20px 20px;
    animation: move 2s linear infinite;
}

/* 模态框增强样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-lg {
    padding: 15px 35px;
    font-size: 1.1rem;
}

.btn-lg small {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* 状态文本样式 */
#statusText {
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
}

/* 文件信息显示 */
.file-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #007bff;
}

.file-info h6 {
    color: #007bff;
    margin-bottom: 10px;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin: 10px 0;
    }
    
    .upload-area {
        min-height: 150px;
        padding: 20px;
    }
    
    .upload-area h5 {
        font-size: 1.1rem;
    }
    
    .btn {
        width: 100%;
        margin: 5px 0;
    }
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

/* 错误提示样式 */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-info {
    background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
    color: #004085;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
