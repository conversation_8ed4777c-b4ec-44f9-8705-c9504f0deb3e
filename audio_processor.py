# -*- coding: utf-8 -*-
"""
音视频处理模块
负责音视频文件的格式转换、音频提取、预处理等功能
"""

import os
import logging
import subprocess
import tempfile
from pathlib import Path

try:
    from moviepy.editor import VideoFileClip, AudioFileClip
    from pydub import AudioSegment
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    logging.warning("MoviePy或pydub未安装，某些功能可能不可用")

class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, temp_dir=None):
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.logger = logging.getLogger(__name__)
        
    def extract_audio_from_video(self, video_path, output_path=None):
        """从视频文件中提取音频"""
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("MoviePy未安装，无法处理视频文件")
            
            if not output_path:
                # 生成临时输出文件名
                video_name = Path(video_path).stem
                output_path = os.path.join(self.temp_dir, f"{video_name}_audio.wav")
            
            self.logger.info(f"开始从视频提取音频: {video_path} -> {output_path}")
            
            # 使用MoviePy提取音频
            with VideoFileClip(video_path) as video:
                if video.audio is None:
                    raise Exception("视频文件不包含音频轨道")
                
                # 提取音频并保存为WAV格式
                video.audio.write_audiofile(
                    output_path,
                    codec='pcm_s16le',  # 16位PCM编码
                    ffmpeg_params=['-ac', '1', '-ar', '16000'],  # 单声道，16kHz采样率
                    verbose=False,
                    logger=None
                )
            
            self.logger.info(f"音频提取完成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"音频提取失败: {str(e)}")
            raise
    
    def convert_audio_format(self, input_path, output_path=None, target_format='wav'):
        """转换音频格式"""
        try:
            if not output_path:
                # 生成输出文件名
                input_name = Path(input_path).stem
                output_path = os.path.join(self.temp_dir, f"{input_name}.{target_format}")
            
            self.logger.info(f"开始转换音频格式: {input_path} -> {output_path}")
            
            if MOVIEPY_AVAILABLE:
                # 使用MoviePy转换
                with AudioFileClip(input_path) as audio:
                    audio.write_audiofile(
                        output_path,
                        codec='pcm_s16le' if target_format == 'wav' else None,
                        ffmpeg_params=['-ac', '1', '-ar', '16000'],  # 单声道，16kHz
                        verbose=False,
                        logger=None
                    )
            else:
                # 使用pydub转换（如果可用）
                try:
                    audio = AudioSegment.from_file(input_path)
                    # 转换为单声道，16kHz采样率
                    audio = audio.set_channels(1).set_frame_rate(16000)
                    audio.export(output_path, format=target_format)
                except:
                    # 使用ffmpeg命令行工具
                    self._convert_with_ffmpeg(input_path, output_path)
            
            self.logger.info(f"音频格式转换完成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"音频格式转换失败: {str(e)}")
            raise
    
    def _convert_with_ffmpeg(self, input_path, output_path):
        """使用ffmpeg命令行工具转换音频"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-ac', '1',  # 单声道
                '-ar', '16000',  # 16kHz采样率
                '-acodec', 'pcm_s16le',  # 16位PCM编码
                '-y',  # 覆盖输出文件
                output_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode != 0:
                raise Exception(f"ffmpeg转换失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            raise Exception("音频转换超时")
        except FileNotFoundError:
            raise Exception("ffmpeg未安装或不在PATH中")
    
    def get_audio_info(self, audio_path):
        """获取音频文件信息"""
        try:
            info = {}
            
            if MOVIEPY_AVAILABLE:
                try:
                    with AudioFileClip(audio_path) as audio:
                        info.update({
                            'duration': audio.duration,
                            'fps': audio.fps,
                            'channels': audio.nchannels if hasattr(audio, 'nchannels') else 1
                        })
                except:
                    pass
            
            # 使用ffprobe获取详细信息
            try:
                info.update(self._get_info_with_ffprobe(audio_path))
            except:
                pass
            
            # 获取文件大小
            if os.path.exists(audio_path):
                info['file_size'] = os.path.getsize(audio_path)
            
            return info
            
        except Exception as e:
            self.logger.error(f"获取音频信息失败: {str(e)}")
            return {}
    
    def _get_info_with_ffprobe(self, audio_path):
        """使用ffprobe获取音频信息"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                audio_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                
                info = {}
                if 'format' in data:
                    format_info = data['format']
                    info.update({
                        'duration': float(format_info.get('duration', 0)),
                        'bit_rate': int(format_info.get('bit_rate', 0)),
                        'format_name': format_info.get('format_name', '')
                    })
                
                if 'streams' in data and data['streams']:
                    stream = data['streams'][0]
                    info.update({
                        'sample_rate': int(stream.get('sample_rate', 0)),
                        'channels': int(stream.get('channels', 1)),
                        'codec_name': stream.get('codec_name', '')
                    })
                
                return info
            
        except (subprocess.TimeoutExpired, FileNotFoundError, json.JSONDecodeError):
            pass
        
        return {}
    
    def split_audio(self, audio_path, chunk_duration=60):
        """将音频文件分割成小块"""
        try:
            chunks = []
            audio_info = self.get_audio_info(audio_path)
            total_duration = audio_info.get('duration', 0)
            
            if total_duration <= chunk_duration:
                # 文件较小，不需要分割
                return [audio_path]
            
            self.logger.info(f"开始分割音频文件，总时长: {total_duration}秒")
            
            if MOVIEPY_AVAILABLE:
                with AudioFileClip(audio_path) as audio:
                    chunk_count = int(total_duration / chunk_duration) + 1
                    
                    for i in range(chunk_count):
                        start_time = i * chunk_duration
                        end_time = min((i + 1) * chunk_duration, total_duration)
                        
                        if start_time >= total_duration:
                            break
                        
                        # 生成分块文件名
                        audio_name = Path(audio_path).stem
                        chunk_path = os.path.join(
                            self.temp_dir, 
                            f"{audio_name}_chunk_{i:03d}.wav"
                        )
                        
                        # 提取音频片段
                        chunk_audio = audio.subclip(start_time, end_time)
                        chunk_audio.write_audiofile(
                            chunk_path,
                            codec='pcm_s16le',
                            verbose=False,
                            logger=None
                        )
                        
                        chunks.append(chunk_path)
                        self.logger.debug(f"创建音频分块: {chunk_path} ({start_time}-{end_time}秒)")
            
            self.logger.info(f"音频分割完成，共{len(chunks)}个分块")
            return chunks
            
        except Exception as e:
            self.logger.error(f"音频分割失败: {str(e)}")
            raise
    
    def cleanup_temp_files(self, file_paths):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path) and file_path.startswith(self.temp_dir):
                    os.remove(file_path)
                    self.logger.debug(f"清理临时文件: {file_path}")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败 {file_path}: {str(e)}")

def process_media_file(file_path, temp_dir=None):
    """处理媒体文件的主函数"""
    processor = AudioProcessor(temp_dir)
    
    try:
        # 判断文件类型
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.3gp']:
            # 视频文件，提取音频
            audio_path = processor.extract_audio_from_video(file_path)
        elif file_ext in ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg', '.wma']:
            # 音频文件，转换格式
            if file_ext != '.wav':
                audio_path = processor.convert_audio_format(file_path)
            else:
                audio_path = file_path
        else:
            raise Exception(f"不支持的文件格式: {file_ext}")
        
        # 获取音频信息
        audio_info = processor.get_audio_info(audio_path)
        
        return {
            'audio_path': audio_path,
            'audio_info': audio_info,
            'processor': processor
        }
        
    except Exception as e:
        logging.error(f"媒体文件处理失败: {str(e)}")
        raise
