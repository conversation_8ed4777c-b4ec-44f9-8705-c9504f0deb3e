---
description: Augment 规则文件的标准格式和最佳实践指南，定义规则创建、组织、命名和内容结构的规范，确保规则系统的一致性和可维护性
globs: *.mdc
alwaysApply: true
---

# Augment 规则系统标准规范

## 使用场景
- 创建新的 Augment 规则文件时
- 更新或重构现有规则文件时
- 需要确保规则文件符合标准格式时
- 团队协作中需要统一规则文件结构时

## 关键规则

### 📁 文件组织规范
- 规则文件必须使用 `.mdc` 扩展名
- 文件名使用 kebab-case 格式
- 私有规则以下划线 `_` 开头，将被 gitignore
- 规则文件必须包含类型后缀：`-{auto|agent|manual|always}.mdc`

### 📋 前置元数据要求
- 必须包含 YAML 前置信息块，使用 `---` 分隔
- `description` 字段限制在 150 字以内，包含关键场景、动作、触发条件、结果
- `globs` 字段指定文件匹配模式，不使用引号，不使用 `{}` 分组
- `alwaysApply` 字段明确指定 true 或 false

### 📝 内容结构要求
- 必须包含：使用场景、关键规则、示例三个核心部分
- 使用简洁的 Markdown 语法
- 关键规则使用列表形式，明确具体的行动指令
- 示例必须包含有效和无效两种情况

### 🏷️ 规则类型分类
- **Auto 规则**: 自动应用，alwaysApply: true
- **Agent 规则**: 特定目的，alwaysApply: false，globs 为空
- **Manual 规则**: 手动触发，alwaysApply: false
- **Always 规则**: 全局应用，alwaysApply: true

## 标准文件模板

### 基础模板结构
```markdown
---
description: 规则描述（150字以内）
globs: 文件匹配模式
alwaysApply: true/false
---

# 规则标题

## 使用场景
- 何时应用此规则
- 前提条件或要求

## 关键规则
- 简洁的、列表形式的行动规则
- 始终执行 X
- 绝不执行 Y

## 示例
<example>
  有效示例及其说明
</example>

<example type="invalid">
  无效示例及其说明
</example>
```

### Glob 模式示例
- 通用规则：`*.mdc`
- 语言特定：`*.js`, `*.ts`, `*.py`
- 测试文件：`*.test.js`, `*.test.ts`
- 文档文件：`docs/**/*.md`, `*.md`
- 配置文件：`*.config.js`, `*.json`, `*.yaml`
- 组件文件：`*.tsx`, `*.jsx`, `*.vue`
- 构建产物：`dist/**/*`

## 示例

<example>
  正确的规则文件结构：
  
  ```markdown
  ---
  description: TypeScript 代码质量规则，确保类型安全、命名规范和最佳实践
  globs: *.ts, *.tsx
  alwaysApply: true
  ---
  
  # TypeScript 代码质量规则
  
  ## 使用场景
  - 编写或修改 TypeScript 文件时
  - 代码审查和重构时
  
  ## 关键规则
  - 始终使用明确的类型注解
  - 避免使用 any 类型
  - 使用 camelCase 命名变量和函数
  
  ## 示例
  <example>
    interface User {
      id: number;
      name: string;
    }
  </example>
  ```
</example>

<example type="invalid">
  错误的规则文件结构：
  
  ```markdown
  # 规则标题（缺少前置元数据）
  
  这是一些规则内容...（缺少标准结构）
  ```
  
  问题：
  - 缺少 YAML 前置信息块
  - 没有标准的章节结构
  - 缺少示例部分
</example>

## 高级特性

### 🎯 规则类型详解

#### Agent 规则特点
- 服务于特定目的，不加载到每个聊天线程
- description 必须提供全面上下文
- globs 为空，alwaysApply: false
- 文件名以 `-agent.mdc` 结尾

#### 条件应用规则
- 当 alwaysApply: false 时，description 必须提供足够上下文
- 帮助 AI 确定何时加载和应用规则
- 专注于明确的行动指令

### 📐 格式指南
- 仅使用 `<example>` XML 标签
- XML 标签内容使用 2 个空格缩进
- 可适当使用表情符号增强可读性
- 支持 Mermaid 图表（避免冗余）

### ⚡ 性能考虑
- 内容长度影响性能，保持简洁
- 专注于帮助 Agent 决策的关键信息
- 避免不必要的解释和冗余内容

## 质量检查清单

### ✅ 必检项目
- [ ] 包含完整的 YAML 前置信息
- [ ] description 字段在 150 字以内
- [ ] 包含使用场景、关键规则、示例三个部分
- [ ] 示例包含有效和无效两种情况
- [ ] 文件名符合命名规范
- [ ] globs 模式正确（无引号，无 {} 分组）

### 🔍 内容质量
- [ ] 关键规则明确具体
- [ ] 示例贴近实际使用场景
- [ ] 内容简洁有效
- [ ] 逻辑结构清晰

## 维护指南

### 🔄 更新流程
1. 检查现有 rules.mdc 文件中的相关规则
2. 确定规则类型和适用范围
3. 按照标准模板更新内容
4. 验证格式和内容质量
5. 测试规则应用效果

### 📊 成功指标
- 规则应用准确性
- 团队采用一致性
- 维护成本降低
- 开发效率提升

## 常见问题解答

### ❓ 规则类型选择
**Q: 何时使用 agent 规则？**
A: 当规则服务于特定目的，不需要在每个聊天线程中加载时使用。例如：特定的代码重构规则、错误修复模式等。

**Q: alwaysApply 如何设置？**
A:
- `true`: 规则应该始终应用于匹配的文件
- `false`: 规则仅在特定条件下应用，需要 AI 判断

### ❓ Glob 模式最佳实践
**Q: 如何编写有效的 glob 模式？**
A:
- 使用具体的文件扩展名：`*.js`, `*.ts`
- 目录匹配：`src/**/*.js`
- 多扩展名：`*.js, *.ts` (不使用 `*.{js,ts}`)
- 避免过于宽泛的模式

### ❓ 内容组织
**Q: 关键规则部分应该包含什么？**
A:
- 明确的行动指令
- 具体的约束条件
- 必须遵循的标准
- 禁止的行为

## 规则创建工作流

### 🚀 快速开始
1. **确定规则目的**: 明确规则要解决的问题
2. **选择规则类型**: 根据应用场景选择合适的类型
3. **编写描述**: 简洁描述规则的核心功能
4. **定义 globs**: 指定规则适用的文件范围
5. **编写内容**: 按照标准模板组织内容
6. **添加示例**: 提供有效和无效的示例
7. **测试验证**: 确保规则按预期工作

### 📋 规则审查检查表
- [ ] 元数据完整且格式正确
- [ ] 描述清晰且在字数限制内
- [ ] 使用场景明确具体
- [ ] 关键规则可执行且明确
- [ ] 示例贴近实际且有代表性
- [ ] 文件命名符合规范
- [ ] 内容简洁有效

## 最佳实践总结

### 🎯 核心原则
- **简洁性**: 内容精炼，避免冗余
- **明确性**: 规则清晰，不产生歧义
- **可执行性**: 规则具体，可以直接应用
- **一致性**: 格式统一，结构标准

### 🔧 实用技巧
- 使用动作词开始规则条目（始终、绝不、必须、避免）
- 提供具体的代码示例而非抽象描述
- 在描述中包含关键词帮助 AI 理解上下文
- 定期审查和更新规则以保持相关性

### ⚠️ 常见陷阱
- 避免过于复杂的 glob 模式
- 不要在规则中包含过多解释性文字
- 避免创建重复或冲突的规则
- 不要忽略示例的重要性

---

**规则创建完成后的响应格式：**
- 自动规则生成成功: {规则文件相对路径及文件名}
- 规则类型: {auto|agent|manual|always}
- 规则描述: {描述字段的确切内容}
