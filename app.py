# -*- coding: utf-8 -*-
"""
音视频转字幕网页应用主文件
基于Flask框架，提供音视频上传、语音识别、字幕生成和AI优化功能
"""

import os
import sys
import logging
import traceback
import uuid
import time
import threading
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler

from flask import Flask, render_template, request, jsonify, send_file, abort, session
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
import magic
import filetype

from config import get_config
from task_processor import TaskProcessor

# 创建Flask应用实例
app = Flask(__name__)

# 加载配置
config_class = get_config()
app.config.from_object(config_class)

# 初始化配置
config_class.init_app(app)

# 全局任务存储（生产环境建议使用Redis）
tasks = {}
task_lock = threading.Lock()

# 创建任务处理器
task_processor = TaskProcessor(app, tasks, task_lock)

def setup_logging():
    """设置日志系统"""
    if not app.debug and not app.testing:
        # 创建日志目录
        if not os.path.exists(app.config['LOG_FOLDER']):
            os.makedirs(app.config['LOG_FOLDER'])
        
        # 设置文件日志处理器
        file_handler = RotatingFileHandler(
            os.path.join(app.config['LOG_FOLDER'], 'app.log'),
            maxBytes=app.config['LOG_MAX_BYTES'],
            backupCount=app.config['LOG_BACKUP_COUNT']
        )
        file_handler.setFormatter(logging.Formatter(app.config['LOG_FORMAT']))
        file_handler.setLevel(app.config['LOG_LEVEL'])
        
        # 添加到应用日志
        app.logger.addHandler(file_handler)
        app.logger.setLevel(app.config['LOG_LEVEL'])
        app.logger.info('音视频转字幕应用启动')

def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def get_file_type(filename):
    """获取文件类型（音频或视频）"""
    if not filename:
        return None

    ext = filename.rsplit('.', 1)[1].lower()
    if ext in app.config['ALLOWED_AUDIO_EXTENSIONS']:
        return 'audio'
    elif ext in app.config['ALLOWED_VIDEO_EXTENSIONS']:
        return 'video'
    return None

def generate_unique_filename(original_filename):
    """生成唯一的文件名"""
    # 获取文件扩展名
    if '.' in original_filename:
        name, ext = original_filename.rsplit('.', 1)
        ext = ext.lower()
    else:
        name = original_filename
        ext = ''

    # 生成唯一标识符
    unique_id = str(uuid.uuid4())
    timestamp = str(int(time.time()))

    # 安全的文件名
    safe_name = secure_filename(name)[:50]  # 限制长度

    # 组合新文件名
    if ext:
        new_filename = f"{safe_name}_{timestamp}_{unique_id}.{ext}"
    else:
        new_filename = f"{safe_name}_{timestamp}_{unique_id}"

    return new_filename

def validate_file_content(file_path):
    """验证文件内容和类型"""
    try:
        # 使用python-magic检测文件类型
        file_mime = magic.from_file(file_path, mime=True)

        # 使用filetype检测文件类型
        kind = filetype.guess(file_path)

        app.logger.info(f"文件MIME类型: {file_mime}")
        if kind:
            app.logger.info(f"文件类型: {kind.extension}, MIME: {kind.mime}")

        # 验证是否为音视频文件
        audio_mimes = [
            'audio/mpeg', 'audio/wav', 'audio/x-wav', 'audio/mp4',
            'audio/aac', 'audio/flac', 'audio/ogg', 'audio/x-ms-wma'
        ]

        video_mimes = [
            'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo',
            'video/x-matroska', 'video/x-ms-wmv', 'video/x-flv', 'video/webm'
        ]

        if file_mime in audio_mimes or file_mime in video_mimes:
            return True, file_mime

        # 如果MIME检测失败，尝试通过扩展名验证
        if kind and (kind.extension in app.config['ALLOWED_EXTENSIONS']):
            return True, kind.mime

        return False, f"不支持的文件类型: {file_mime}"

    except Exception as e:
        app.logger.error(f"文件内容验证失败: {str(e)}")
        return False, f"文件验证失败: {str(e)}"

def get_file_info(file_path):
    """获取文件详细信息"""
    try:
        stat = os.stat(file_path)
        file_size = stat.st_size

        # 获取文件类型
        is_valid, mime_type = validate_file_content(file_path)

        info = {
            'size': file_size,
            'size_mb': round(file_size / 1024 / 1024, 2),
            'mime_type': mime_type if is_valid else 'unknown',
            'is_valid': is_valid,
            'created_time': datetime.fromtimestamp(stat.st_ctime),
            'modified_time': datetime.fromtimestamp(stat.st_mtime)
        }

        return info

    except Exception as e:
        app.logger.error(f"获取文件信息失败: {str(e)}")
        return None

def create_task(task_type, file_info, options=None):
    """创建处理任务"""
    task_id = str(uuid.uuid4())

    task = {
        'id': task_id,
        'type': task_type,
        'status': 'created',
        'progress': 0,
        'message': '任务已创建',
        'file_info': file_info,
        'options': options or {},
        'created_time': datetime.now(),
        'updated_time': datetime.now(),
        'result': None,
        'error': None
    }

    with task_lock:
        tasks[task_id] = task

    app.logger.info(f"创建任务: {task_id}, 类型: {task_type}")
    return task_id

def update_task(task_id, **kwargs):
    """更新任务状态"""
    with task_lock:
        if task_id in tasks:
            task = tasks[task_id]
            for key, value in kwargs.items():
                task[key] = value
            task['updated_time'] = datetime.now()
            app.logger.debug(f"更新任务 {task_id}: {kwargs}")
            return True
    return False

def get_task(task_id):
    """获取任务信息"""
    with task_lock:
        return tasks.get(task_id, None)

def cleanup_old_files():
    """清理过期文件"""
    try:
        cleanup_hours = app.config.get('FILE_CLEANUP_HOURS', 24)
        cutoff_time = datetime.now() - timedelta(hours=cleanup_hours)

        # 清理上传文件
        upload_dir = app.config['UPLOAD_FOLDER']
        temp_dir = app.config['TEMP_FOLDER']

        for directory in [upload_dir, temp_dir]:
            if os.path.exists(directory):
                for filename in os.listdir(directory):
                    file_path = os.path.join(directory, filename)
                    if os.path.isfile(file_path):
                        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                        if file_time < cutoff_time:
                            try:
                                os.remove(file_path)
                                app.logger.info(f"清理过期文件: {file_path}")
                            except Exception as e:
                                app.logger.error(f"清理文件失败 {file_path}: {str(e)}")

        # 清理过期任务
        with task_lock:
            expired_tasks = []
            for task_id, task in tasks.items():
                if task['created_time'] < cutoff_time:
                    expired_tasks.append(task_id)

            for task_id in expired_tasks:
                del tasks[task_id]
                app.logger.info(f"清理过期任务: {task_id}")

    except Exception as e:
        app.logger.error(f"清理过期文件失败: {str(e)}")

# 启动时清理一次过期文件
cleanup_old_files()

# 错误处理装饰器
@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    app.logger.warning(f'404错误: {request.url}')
    return render_template('error.html', 
                         error_code=404, 
                         error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    app.logger.error(f'500错误: {str(error)}')
    app.logger.error(traceback.format_exc())
    return render_template('error.html', 
                         error_code=500, 
                         error_message='服务器内部错误'), 500

@app.errorhandler(413)
@app.errorhandler(RequestEntityTooLarge)
def file_too_large(error):
    """文件过大错误处理"""
    app.logger.warning(f'文件过大错误: {request.url}')
    return jsonify({
        'success': False,
        'error': '文件大小超过限制（最大500MB）'
    }), 413

@app.errorhandler(Exception)
def handle_exception(error):
    """全局异常处理"""
    app.logger.error(f'未处理的异常: {str(error)}')
    app.logger.error(traceback.format_exc())
    
    if app.debug:
        return str(error), 500
    
    return render_template('error.html', 
                         error_code=500, 
                         error_message='系统发生错误，请稍后重试'), 500

# 路由定义
@app.route('/')
def index():
    """主页路由"""
    try:
        app.logger.info('用户访问主页')
        return render_template('index.html')
    except Exception as e:
        app.logger.error(f'主页渲染错误: {str(e)}')
        return render_template('error.html', 
                             error_code=500, 
                             error_message='页面加载失败'), 500

@app.route('/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/config')
def get_config_info():
    """获取前端需要的配置信息"""
    return jsonify({
        'max_file_size': app.config['MAX_CONTENT_LENGTH'],
        'allowed_extensions': list(app.config['ALLOWED_EXTENSIONS']),
        'audio_extensions': list(app.config['ALLOWED_AUDIO_EXTENSIONS']),
        'video_extensions': list(app.config['ALLOWED_VIDEO_EXTENSIONS'])
    })

@app.route('/upload', methods=['POST'])
def upload_file():
    """文件上传接口"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        file = request.files['file']

        # 检查文件名
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '文件名不能为空'
            }), 400

        # 验证文件扩展名
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': f'不支持的文件格式。支持的格式：{", ".join(app.config["ALLOWED_EXTENSIONS"])}'
            }), 400

        # 生成唯一文件名
        unique_filename = generate_unique_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        # 保存文件
        try:
            file.save(file_path)
            app.logger.info(f"文件保存成功: {file_path}")
        except Exception as e:
            app.logger.error(f"文件保存失败: {str(e)}")
            return jsonify({
                'success': False,
                'error': '文件保存失败'
            }), 500

        # 验证文件内容
        is_valid, mime_type = validate_file_content(file_path)
        if not is_valid:
            # 删除无效文件
            try:
                os.remove(file_path)
            except:
                pass
            return jsonify({
                'success': False,
                'error': f'文件内容验证失败: {mime_type}'
            }), 400

        # 获取文件信息
        file_info = get_file_info(file_path)
        if not file_info:
            # 删除文件
            try:
                os.remove(file_path)
            except:
                pass
            return jsonify({
                'success': False,
                'error': '无法获取文件信息'
            }), 500

        # 添加文件路径信息
        file_info.update({
            'original_name': file.filename,
            'unique_name': unique_filename,
            'file_path': file_path,
            'file_type': get_file_type(file.filename)
        })

        # 获取处理选项
        ai_optimize = request.form.get('ai_optimize', 'true').lower() == 'true'
        options = {
            'ai_optimize': ai_optimize
        }

        # 创建处理任务
        task_id = create_task('audio_to_subtitle', file_info, options)

        # 在会话中保存任务ID
        session['current_task_id'] = task_id

        return jsonify({
            'success': True,
            'task_id': task_id,
            'file_info': {
                'name': file.filename,
                'size': file_info['size'],
                'size_mb': file_info['size_mb'],
                'type': file_info['file_type'],
                'mime_type': file_info['mime_type']
            },
            'message': '文件上传成功，准备开始处理'
        })

    except RequestEntityTooLarge:
        return jsonify({
            'success': False,
            'error': f'文件大小超过限制（最大{app.config["MAX_CONTENT_LENGTH"] // 1024 // 1024}MB）'
        }), 413

    except Exception as e:
        app.logger.error(f"文件上传异常: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': '文件上传失败，请重试'
        }), 500

@app.route('/progress/<task_id>')
def get_progress(task_id):
    """获取任务进度"""
    try:
        task = get_task(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

        # 计算已用时间
        elapsed_time = datetime.now() - task['created_time']
        elapsed_seconds = int(elapsed_time.total_seconds())

        response_data = {
            'success': True,
            'task_id': task_id,
            'status': task['status'],
            'progress': task['progress'],
            'message': task['message'],
            'elapsed_time': elapsed_seconds,
            'completed': task['status'] in ['completed', 'failed'],
            'error': task.get('error'),
            'result': task.get('result')
        }

        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"获取进度异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取进度失败'
        }), 500

@app.route('/cancel/<task_id>', methods=['POST'])
def cancel_task(task_id):
    """取消任务"""
    try:
        task = get_task(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

        if task['status'] in ['completed', 'failed', 'cancelled']:
            return jsonify({
                'success': False,
                'error': '任务已完成或已取消'
            }), 400

        # 更新任务状态
        update_task(task_id, status='cancelled', message='任务已取消')

        app.logger.info(f"任务已取消: {task_id}")

        return jsonify({
            'success': True,
            'message': '任务已取消'
        })

    except Exception as e:
        app.logger.error(f"取消任务异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '取消任务失败'
        }), 500

@app.route('/process/<task_id>', methods=['POST'])
def start_processing(task_id):
    """开始处理任务"""
    try:
        task = get_task(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

        if task['status'] != 'created':
            return jsonify({
                'success': False,
                'error': f'任务状态不正确，当前状态: {task["status"]}'
            }), 400

        # 启动任务处理
        if task_processor.start_processing(task_id):
            return jsonify({
                'success': True,
                'message': '任务处理已开始'
            })
        else:
            return jsonify({
                'success': False,
                'error': '启动任务处理失败'
            }), 500

    except Exception as e:
        app.logger.error(f"开始处理任务异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '启动处理失败'
        }), 500

@app.route('/tasks')
def list_tasks():
    """获取任务列表"""
    try:
        # 只返回当前会话的任务
        current_task_id = session.get('current_task_id')

        if current_task_id:
            task = get_task(current_task_id)
            if task:
                return jsonify({
                    'success': True,
                    'tasks': [{
                        'id': task['id'],
                        'status': task['status'],
                        'progress': task['progress'],
                        'message': task['message'],
                        'created_time': task['created_time'].isoformat(),
                        'file_name': task['file_info']['original_name']
                    }]
                })

        return jsonify({
            'success': True,
            'tasks': []
        })

    except Exception as e:
        app.logger.error(f"获取任务列表异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取任务列表失败'
        }), 500

@app.route('/cleanup')
def cleanup_files():
    """手动清理过期文件"""
    try:
        cleanup_old_files()

        # 清理已完成的任务
        cleaned_count = task_processor.cleanup_completed_tasks()

        return jsonify({
            'success': True,
            'message': f'清理完成，清理了{cleaned_count}个过期任务'
        })

    except Exception as e:
        app.logger.error(f"清理文件异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '清理失败'
        }), 500

@app.route('/download/<task_id>')
def download_subtitle(task_id):
    """下载字幕文件"""
    try:
        # 获取下载类型
        download_type = request.args.get('type', 'original')

        # 获取任务信息
        task = get_task(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

        if task['status'] != 'completed':
            return jsonify({
                'success': False,
                'error': '任务未完成'
            }), 400

        # 获取结果信息
        result = task.get('result', {})

        # 确定文件路径
        if download_type == 'optimized':
            file_path = result.get('optimized_subtitle_path')
            if not file_path:
                return jsonify({
                    'success': False,
                    'error': 'AI优化字幕不可用'
                }), 404
        else:
            file_path = result.get('original_subtitle_path')
            if not file_path:
                return jsonify({
                    'success': False,
                    'error': '原始字幕不可用'
                }), 404

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': '字幕文件不存在'
            }), 404

        # 生成下载文件名
        original_name = task['file_info']['original_name']
        name_without_ext = os.path.splitext(original_name)[0]

        if download_type == 'optimized':
            download_filename = f"{name_without_ext}_AI优化字幕.srt"
        else:
            download_filename = f"{name_without_ext}_字幕.srt"

        app.logger.info(f"下载字幕文件: {file_path} -> {download_filename}")

        return send_file(
            file_path,
            as_attachment=True,
            download_name=download_filename,
            mimetype='text/plain; charset=utf-8'
        )

    except Exception as e:
        app.logger.error(f"下载字幕文件异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '下载失败'
        }), 500

@app.route('/preview/<task_id>')
def preview_subtitle(task_id):
    """预览字幕内容"""
    try:
        # 获取预览类型
        preview_type = request.args.get('type', 'original')

        # 获取任务信息
        task = get_task(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

        if task['status'] != 'completed':
            return jsonify({
                'success': False,
                'error': '任务未完成'
            }), 400

        # 获取结果信息
        result = task.get('result', {})

        # 确定文件路径
        if preview_type == 'optimized':
            file_path = result.get('optimized_subtitle_path')
        else:
            file_path = result.get('original_subtitle_path')

        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': '字幕文件不存在'
            }), 404

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析SRT内容为预览格式
        preview_data = parse_srt_for_preview(content)

        return jsonify({
            'success': True,
            'content': content,
            'preview': preview_data,
            'type': preview_type
        })

    except Exception as e:
        app.logger.error(f"预览字幕异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '预览失败'
        }), 500

def parse_srt_for_preview(srt_content, max_entries=5):
    """解析SRT内容用于预览"""
    try:
        entries = []
        lines = srt_content.strip().split('\n')

        current_entry = {}
        line_type = 'index'  # index, time, text

        for line in lines:
            line = line.strip()

            if not line:
                if current_entry:
                    entries.append(current_entry)
                    current_entry = {}
                    line_type = 'index'
                continue

            if line_type == 'index' and line.isdigit():
                current_entry['index'] = int(line)
                line_type = 'time'
            elif line_type == 'time' and '-->' in line:
                times = line.split(' --> ')
                current_entry['start'] = times[0].strip()
                current_entry['end'] = times[1].strip()
                current_entry['text'] = ''
                line_type = 'text'
            elif line_type == 'text':
                if current_entry['text']:
                    current_entry['text'] += '\n'
                current_entry['text'] += line

        # 添加最后一个条目
        if current_entry:
            entries.append(current_entry)

        # 返回前几个条目用于预览
        return entries[:max_entries]

    except Exception as e:
        app.logger.error(f"解析SRT预览失败: {str(e)}")
        return []

# 上下文处理器
@app.context_processor
def inject_config():
    """向模板注入配置信息"""
    return {
        'config': {
            'max_file_size_mb': app.config['MAX_CONTENT_LENGTH'] // (1024 * 1024),
            'allowed_extensions': ', '.join(app.config['ALLOWED_EXTENSIONS']),
            'app_name': '音视频转字幕工具'
        }
    }

# 请求前处理
@app.before_request
def before_request():
    """请求前处理"""
    # 记录请求信息
    if not request.endpoint == 'health_check':
        app.logger.info(f'请求: {request.method} {request.url}')

# 请求后处理
@app.after_request
def after_request(response):
    """请求后处理"""
    # 添加安全头
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    return response

if __name__ == '__main__':
    # 设置日志
    setup_logging()
    
    # 启动应用
    app.logger.info('启动音视频转字幕应用')
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=app.config.get('DEBUG', False),
        threaded=True
    )
