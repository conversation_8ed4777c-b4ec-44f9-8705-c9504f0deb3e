# -*- coding: utf-8 -*-
"""
音视频转字幕网页应用主文件
基于Flask框架，提供音视频上传、语音识别、字幕生成和AI优化功能
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from logging.handlers import RotatingFileHandler

from flask import Flask, render_template, request, jsonify, send_file, abort
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge

from config import get_config

# 创建Flask应用实例
app = Flask(__name__)

# 加载配置
config_class = get_config()
app.config.from_object(config_class)

# 初始化配置
config_class.init_app(app)

def setup_logging():
    """设置日志系统"""
    if not app.debug and not app.testing:
        # 创建日志目录
        if not os.path.exists(app.config['LOG_FOLDER']):
            os.makedirs(app.config['LOG_FOLDER'])
        
        # 设置文件日志处理器
        file_handler = RotatingFileHandler(
            os.path.join(app.config['LOG_FOLDER'], 'app.log'),
            maxBytes=app.config['LOG_MAX_BYTES'],
            backupCount=app.config['LOG_BACKUP_COUNT']
        )
        file_handler.setFormatter(logging.Formatter(app.config['LOG_FORMAT']))
        file_handler.setLevel(app.config['LOG_LEVEL'])
        
        # 添加到应用日志
        app.logger.addHandler(file_handler)
        app.logger.setLevel(app.config['LOG_LEVEL'])
        app.logger.info('音视频转字幕应用启动')

def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def get_file_type(filename):
    """获取文件类型（音频或视频）"""
    if not filename:
        return None
    
    ext = filename.rsplit('.', 1)[1].lower()
    if ext in app.config['ALLOWED_AUDIO_EXTENSIONS']:
        return 'audio'
    elif ext in app.config['ALLOWED_VIDEO_EXTENSIONS']:
        return 'video'
    return None

# 错误处理装饰器
@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    app.logger.warning(f'404错误: {request.url}')
    return render_template('error.html', 
                         error_code=404, 
                         error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    app.logger.error(f'500错误: {str(error)}')
    app.logger.error(traceback.format_exc())
    return render_template('error.html', 
                         error_code=500, 
                         error_message='服务器内部错误'), 500

@app.errorhandler(413)
@app.errorhandler(RequestEntityTooLarge)
def file_too_large(error):
    """文件过大错误处理"""
    app.logger.warning(f'文件过大错误: {request.url}')
    return jsonify({
        'success': False,
        'error': '文件大小超过限制（最大500MB）'
    }), 413

@app.errorhandler(Exception)
def handle_exception(error):
    """全局异常处理"""
    app.logger.error(f'未处理的异常: {str(error)}')
    app.logger.error(traceback.format_exc())
    
    if app.debug:
        return str(error), 500
    
    return render_template('error.html', 
                         error_code=500, 
                         error_message='系统发生错误，请稍后重试'), 500

# 路由定义
@app.route('/')
def index():
    """主页路由"""
    try:
        app.logger.info('用户访问主页')
        return render_template('index.html')
    except Exception as e:
        app.logger.error(f'主页渲染错误: {str(e)}')
        return render_template('error.html', 
                             error_code=500, 
                             error_message='页面加载失败'), 500

@app.route('/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/config')
def get_config_info():
    """获取前端需要的配置信息"""
    return jsonify({
        'max_file_size': app.config['MAX_CONTENT_LENGTH'],
        'allowed_extensions': list(app.config['ALLOWED_EXTENSIONS']),
        'audio_extensions': list(app.config['ALLOWED_AUDIO_EXTENSIONS']),
        'video_extensions': list(app.config['ALLOWED_VIDEO_EXTENSIONS'])
    })

# 上下文处理器
@app.context_processor
def inject_config():
    """向模板注入配置信息"""
    return {
        'config': {
            'max_file_size_mb': app.config['MAX_CONTENT_LENGTH'] // (1024 * 1024),
            'allowed_extensions': ', '.join(app.config['ALLOWED_EXTENSIONS']),
            'app_name': '音视频转字幕工具'
        }
    }

# 请求前处理
@app.before_request
def before_request():
    """请求前处理"""
    # 记录请求信息
    if not request.endpoint == 'health_check':
        app.logger.info(f'请求: {request.method} {request.url}')

# 请求后处理
@app.after_request
def after_request(response):
    """请求后处理"""
    # 添加安全头
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    return response

if __name__ == '__main__':
    # 设置日志
    setup_logging()
    
    # 启动应用
    app.logger.info('启动音视频转字幕应用')
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=app.config.get('DEBUG', False),
        threaded=True
    )
