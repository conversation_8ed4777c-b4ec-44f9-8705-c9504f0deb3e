# -*- coding: utf-8 -*-
"""
基础功能测试脚本
测试配置加载、Flask应用创建等基础功能
"""

import sys
import os

def test_config():
    """测试配置加载"""
    try:
        from config import get_config, Config
        config_class = get_config()
        print(f"✓ 配置加载成功：{config_class.__name__}")
        
        # 测试配置属性
        config = Config()
        print(f"✓ 上传目录：{config.UPLOAD_FOLDER}")
        print(f"✓ 支持格式：{len(config.ALLOWED_EXTENSIONS)}种")
        print(f"✓ 最大文件大小：{config.MAX_CONTENT_LENGTH // 1024 // 1024}MB")
        
        return True
    except Exception as e:
        print(f"❌ 配置加载失败：{e}")
        return False

def test_flask_app():
    """测试Flask应用创建"""
    try:
        from app import app
        print(f"✓ Flask应用创建成功")
        print(f"✓ 应用名称：{app.name}")
        print(f"✓ 调试模式：{app.debug}")
        
        # 测试路由
        with app.test_client() as client:
            response = client.get('/health')
            if response.status_code == 200:
                print("✓ 健康检查接口正常")
            else:
                print(f"⚠️ 健康检查接口异常：{response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ Flask应用创建失败：{e}")
        return False

def test_directories():
    """测试目录结构"""
    required_dirs = ['uploads', 'temp', 'logs', 'static', 'templates']
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ 目录存在：{directory}")
        else:
            print(f"❌ 目录缺失：{directory}")
            return False
    
    return True

def test_templates():
    """测试模板文件"""
    templates = ['templates/index.html', 'templates/error.html']
    
    for template in templates:
        if os.path.exists(template):
            print(f"✓ 模板存在：{template}")
        else:
            print(f"❌ 模板缺失：{template}")
            return False
    
    return True

def test_static_files():
    """测试静态文件"""
    static_files = ['static/css/style.css', 'static/js/main.js']
    
    for static_file in static_files:
        if os.path.exists(static_file):
            print(f"✓ 静态文件存在：{static_file}")
        else:
            print(f"❌ 静态文件缺失：{static_file}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("音视频转字幕应用 - 基础功能测试")
    print("=" * 50)
    
    tests = [
        ("目录结构测试", test_directories),
        ("配置加载测试", test_config),
        ("Flask应用测试", test_flask_app),
        ("模板文件测试", test_templates),
        ("静态文件测试", test_static_files),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常：{e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("📝 步骤一：项目结构和基础配置 - 完成")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
