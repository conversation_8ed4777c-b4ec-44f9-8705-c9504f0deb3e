# -*- coding: utf-8 -*-
"""
前端界面测试脚本
测试HTML模板、CSS样式、JavaScript功能等
"""

import os
import sys

def test_html_templates():
    """测试HTML模板"""
    print("📋 HTML模板测试")
    print("-" * 30)
    
    templates = {
        'templates/index.html': '主页模板',
        'templates/error.html': '错误页面模板'
    }
    
    for template_path, description in templates.items():
        if os.path.exists(template_path):
            print(f"✓ {description}存在：{template_path}")
            
            # 检查HTML结构
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 基本HTML结构检查
                if '<!DOCTYPE html>' in content:
                    print(f"  ✓ DOCTYPE声明正确")
                else:
                    print(f"  ❌ 缺少DOCTYPE声明")
                
                if 'lang="zh-CN"' in content:
                    print(f"  ✓ 语言设置正确")
                else:
                    print(f"  ⚠️ 语言设置可能不正确")
                
                if 'charset="UTF-8"' in content:
                    print(f"  ✓ 字符编码正确")
                else:
                    print(f"  ❌ 字符编码设置有问题")
                
                # 检查Bootstrap和其他依赖
                if 'bootstrap' in content:
                    print(f"  ✓ 包含Bootstrap框架")
                else:
                    print(f"  ⚠️ 未检测到Bootstrap框架")
                
                if 'font-awesome' in content or 'fas fa-' in content:
                    print(f"  ✓ 包含Font Awesome图标")
                else:
                    print(f"  ⚠️ 未检测到Font Awesome图标")
                    
            except Exception as e:
                print(f"  ❌ 读取模板失败：{e}")
        else:
            print(f"❌ {description}缺失：{template_path}")
    
    return True

def test_css_files():
    """测试CSS文件"""
    print("\n📋 CSS样式文件测试")
    print("-" * 30)
    
    css_files = {
        'static/css/style.css': '主样式文件'
    }
    
    for css_path, description in css_files.items():
        if os.path.exists(css_path):
            print(f"✓ {description}存在：{css_path}")
            
            try:
                with open(css_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查关键样式类
                key_classes = [
                    '.upload-area',
                    '.progress-bar',
                    '.card',
                    '.btn',
                    '.feature-item',
                    '.step-indicator'
                ]
                
                for class_name in key_classes:
                    if class_name in content:
                        print(f"  ✓ 包含样式类：{class_name}")
                    else:
                        print(f"  ⚠️ 缺少样式类：{class_name}")
                
                # 检查响应式设计
                if '@media' in content:
                    print(f"  ✓ 包含响应式设计")
                else:
                    print(f"  ⚠️ 可能缺少响应式设计")
                
                # 检查动画效果
                if '@keyframes' in content or 'animation:' in content:
                    print(f"  ✓ 包含动画效果")
                else:
                    print(f"  ⚠️ 可能缺少动画效果")
                    
            except Exception as e:
                print(f"  ❌ 读取CSS文件失败：{e}")
        else:
            print(f"❌ {description}缺失：{css_path}")
    
    return True

def test_js_files():
    """测试JavaScript文件"""
    print("\n📋 JavaScript文件测试")
    print("-" * 30)
    
    js_files = {
        'static/js/main.js': '主JavaScript文件'
    }
    
    for js_path, description in js_files.items():
        if os.path.exists(js_path):
            print(f"✓ {description}存在：{js_path}")
            
            try:
                with open(js_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查关键函数
                key_functions = [
                    'initializeApp',
                    'setupEventListeners',
                    'handleFileSelect',
                    'validateFile',
                    'showFileInfo',
                    'uploadFile',
                    'showProgress',
                    'downloadFile'
                ]
                
                for func_name in key_functions:
                    if f'function {func_name}' in content or f'{func_name} =' in content:
                        print(f"  ✓ 包含函数：{func_name}")
                    else:
                        print(f"  ⚠️ 可能缺少函数：{func_name}")
                
                # 检查事件监听
                if 'addEventListener' in content:
                    print(f"  ✓ 包含事件监听器")
                else:
                    print(f"  ⚠️ 可能缺少事件监听器")
                
                # 检查AJAX请求
                if 'fetch(' in content or 'XMLHttpRequest' in content:
                    print(f"  ✓ 包含AJAX请求功能")
                else:
                    print(f"  ⚠️ 可能缺少AJAX请求功能")
                    
            except Exception as e:
                print(f"  ❌ 读取JavaScript文件失败：{e}")
        else:
            print(f"❌ {description}缺失：{js_path}")
    
    return True

def test_static_structure():
    """测试静态文件结构"""
    print("\n📋 静态文件结构测试")
    print("-" * 30)
    
    required_dirs = [
        'static',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ 目录存在：{directory}")
        else:
            print(f"❌ 目录缺失：{directory}")
    
    return True

def test_template_integration():
    """测试模板集成"""
    print("\n📋 模板集成测试")
    print("-" * 30)
    
    try:
        from app import app
        
        with app.test_client() as client:
            # 测试主页
            response = client.get('/')
            if response.status_code == 200:
                print("✓ 主页路由正常")
                
                # 检查响应内容
                content = response.get_data(as_text=True)
                
                if '音视频转字幕' in content:
                    print("  ✓ 页面标题正确")
                else:
                    print("  ⚠️ 页面标题可能有问题")
                
                if 'upload-area' in content:
                    print("  ✓ 上传区域存在")
                else:
                    print("  ⚠️ 上传区域可能缺失")
                
                if 'progressCard' in content:
                    print("  ✓ 进度显示区域存在")
                else:
                    print("  ⚠️ 进度显示区域可能缺失")
                    
            else:
                print(f"❌ 主页路由异常：{response.status_code}")
            
            # 测试配置接口
            response = client.get('/api/config')
            if response.status_code == 200:
                print("✓ 配置接口正常")
                
                try:
                    import json
                    config_data = json.loads(response.get_data(as_text=True))
                    
                    if 'allowed_extensions' in config_data:
                        print("  ✓ 配置数据包含允许的扩展名")
                    else:
                        print("  ⚠️ 配置数据可能不完整")
                        
                except Exception as e:
                    print(f"  ⚠️ 配置数据解析失败：{e}")
            else:
                print(f"❌ 配置接口异常：{response.status_code}")
                
    except Exception as e:
        print(f"❌ 模板集成测试失败：{e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("音视频转字幕应用 - 前端界面测试")
    print("=" * 50)
    
    tests = [
        ("静态文件结构测试", test_static_structure),
        ("HTML模板测试", test_html_templates),
        ("CSS样式文件测试", test_css_files),
        ("JavaScript文件测试", test_js_files),
        ("模板集成测试", test_template_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常：{e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有前端界面测试通过！")
        print("📝 步骤二：前端界面开发 - 完成")
        return True
    else:
        print("⚠️ 部分测试失败，请检查前端代码")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
