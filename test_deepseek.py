# -*- coding: utf-8 -*-
"""
DeepSeek AI字幕优化功能测试脚本
测试AI优化API集成、字幕优化等功能
"""

import os
import sys
import json

def test_deepseek_module_import():
    """测试DeepSeek模块导入"""
    print("📋 DeepSeek模块导入测试")
    print("-" * 30)
    
    try:
        from deepseek_ai import DeepSeekAI, create_deepseek_service
        print("✓ DeepSeek模块导入成功")
        
        # 测试模块类
        if hasattr(DeepSeekAI, 'optimize_subtitle_text'):
            print("✓ 字幕优化方法存在")
        else:
            print("❌ 字幕优化方法不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ DeepSeek模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ DeepSeek模块测试失败: {e}")
        return False

def test_deepseek_service_creation():
    """测试DeepSeek服务创建"""
    print("\n📋 DeepSeek服务创建测试")
    print("-" * 30)
    
    try:
        from app import app
        from deepseek_ai import create_deepseek_service
        
        with app.app_context():
            config = app.config
            
            # 检查配置
            if config.get('DEEPSEEK_API_KEY'):
                print("✓ DeepSeek API密钥已配置")
                
                try:
                    # 尝试创建DeepSeek服务
                    deepseek_service = create_deepseek_service(config)
                    print("✓ DeepSeek服务创建成功")
                    
                    # 检查服务属性
                    if hasattr(deepseek_service, 'api_key') and hasattr(deepseek_service, 'api_url'):
                        print("✓ DeepSeek服务属性正确")
                    else:
                        print("⚠️ DeepSeek服务属性可能不完整")
                        
                except Exception as e:
                    print(f"⚠️ DeepSeek服务创建失败: {e}")
            else:
                print("⚠️ DeepSeek API密钥未配置")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek服务创建测试失败: {e}")
        return False

def test_srt_parsing():
    """测试SRT解析功能"""
    print("\n📋 SRT解析测试")
    print("-" * 30)
    
    try:
        from deepseek_ai import DeepSeekAI
        
        # 创建一个模拟的DeepSeek实例
        class MockDeepSeek:
            def _parse_srt_content(self, srt_content):
                entries = []
                lines = srt_content.strip().split('\n')
                
                current_entry = {}
                line_type = 'index'
                
                for line in lines:
                    line = line.strip()
                    
                    if not line:
                        if current_entry and 'text' in current_entry:
                            entries.append(current_entry)
                            current_entry = {}
                            line_type = 'index'
                        continue
                    
                    if line_type == 'index' and line.isdigit():
                        current_entry['index'] = int(line)
                        line_type = 'time'
                    elif line_type == 'time' and '-->' in line:
                        times = line.split(' --> ')
                        current_entry['start_time'] = times[0].strip()
                        current_entry['end_time'] = times[1].strip()
                        current_entry['text'] = ''
                        line_type = 'text'
                    elif line_type == 'text':
                        if current_entry['text']:
                            current_entry['text'] += '\n'
                        current_entry['text'] += line
                
                if current_entry and 'text' in current_entry:
                    entries.append(current_entry)
                
                return entries
        
        mock_deepseek = MockDeepSeek()
        
        # 测试SRT内容
        test_srt = """1
00:00:00,000 --> 00:00:05,000
这是第一条字幕

2
00:00:05,000 --> 00:00:10,000
这是第二条字幕，可能有一些语音识别错误

3
00:00:10,000 --> 00:00:15,000
这是第三条字幕"""
        
        # 解析SRT
        entries = mock_deepseek._parse_srt_content(test_srt)
        
        if len(entries) == 3:
            print("✓ SRT解析条目数量正确")
            
            # 检查第一个条目
            first_entry = entries[0]
            if (first_entry.get('index') == 1 and 
                first_entry.get('start_time') == '00:00:00,000' and
                first_entry.get('end_time') == '00:00:05,000' and
                first_entry.get('text') == '这是第一条字幕'):
                print("✓ SRT解析内容正确")
            else:
                print(f"❌ SRT解析内容错误: {first_entry}")
        else:
            print(f"❌ SRT解析条目数量错误: {len(entries)}")
        
        return True
        
    except Exception as e:
        print(f"❌ SRT解析测试失败: {e}")
        return False

def test_optimization_prompt():
    """测试优化提示词构建"""
    print("\n📋 优化提示词测试")
    print("-" * 30)
    
    try:
        from deepseek_ai import DeepSeekAI
        
        # 创建模拟的DeepSeek实例
        class MockDeepSeek:
            def _build_optimization_prompt(self, batch):
                texts = []
                for i, entry in enumerate(batch):
                    texts.append(f"{i+1}. {entry['text']}")
                
                batch_text = '\n'.join(texts)
                
                prompt = f"""请对以下字幕文本进行优化，要求：

1. 保持原意不变，只优化语言表达
2. 修正语音识别可能的错误
3. 规范标点符号使用
4. 让语言更加自然流畅
5. 保持字幕的简洁性
6. 每条字幕保持独立完整
7. 返回格式与输入格式完全一致（序号. 内容）

原始字幕：
{batch_text}

请返回优化后的字幕："""
                
                return prompt
        
        mock_deepseek = MockDeepSeek()
        
        # 测试批次
        test_batch = [
            {'text': '这是一个测试字幕'},
            {'text': '这里可能有一些错误需要修正'}
        ]
        
        prompt = mock_deepseek._build_optimization_prompt(test_batch)
        
        if '请对以下字幕文本进行优化' in prompt:
            print("✓ 优化提示词包含基本要求")
        else:
            print("❌ 优化提示词格式错误")
        
        if '1. 这是一个测试字幕' in prompt:
            print("✓ 优化提示词包含测试内容")
        else:
            print("❌ 优化提示词内容错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化提示词测试失败: {e}")
        return False

def test_optimization_result_parsing():
    """测试优化结果解析"""
    print("\n📋 优化结果解析测试")
    print("-" * 30)
    
    try:
        from deepseek_ai import DeepSeekAI
        import re
        
        # 创建模拟的DeepSeek实例
        class MockDeepSeek:
            def _parse_optimization_result(self, original_batch, api_response):
                optimized_batch = []
                
                pattern = r'(\d+)\.\s*(.+?)(?=\n\d+\.|$)'
                matches = re.findall(pattern, api_response, re.DOTALL)
                
                for i, entry in enumerate(original_batch):
                    optimized_entry = entry.copy()
                    
                    optimized_text = None
                    for match in matches:
                        if int(match[0]) == i + 1:
                            optimized_text = match[1].strip()
                            break
                    
                    if optimized_text:
                        optimized_entry['text'] = optimized_text
                        optimized_entry['optimized'] = True
                    else:
                        optimized_entry['optimized'] = False
                    
                    optimized_batch.append(optimized_entry)
                
                return optimized_batch
            
            def _clean_optimized_text(self, text):
                text = re.sub(r'\s+', ' ', text)
                text = text.strip('"\'""''')
                text = re.sub(r'\s+([，。！？；：])', r'\1', text)
                return text.strip()
        
        mock_deepseek = MockDeepSeek()
        
        # 测试原始批次
        original_batch = [
            {'text': '这是原始字幕', 'index': 1},
            {'text': '这里有错误', 'index': 2}
        ]
        
        # 模拟API响应
        api_response = """1. 这是优化后的字幕
2. 这里已经修正了错误"""
        
        # 解析结果
        optimized_batch = mock_deepseek._parse_optimization_result(original_batch, api_response)
        
        if len(optimized_batch) == 2:
            print("✓ 优化结果解析数量正确")
            
            if optimized_batch[0]['text'] == '这是优化后的字幕' and optimized_batch[0]['optimized']:
                print("✓ 优化结果解析内容正确")
            else:
                print(f"❌ 优化结果解析内容错误: {optimized_batch[0]}")
        else:
            print(f"❌ 优化结果解析数量错误: {len(optimized_batch)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化结果解析测试失败: {e}")
        return False

def test_task_processor_integration():
    """测试任务处理器集成"""
    print("\n📋 任务处理器集成测试")
    print("-" * 30)
    
    try:
        from task_processor import TaskProcessor
        from app import app, tasks, task_lock
        
        # 创建任务处理器
        processor = TaskProcessor(app, tasks, task_lock)
        print("✓ 任务处理器创建成功")
        
        # 测试任务处理器方法
        if hasattr(processor, '_perform_ai_optimization'):
            print("✓ AI优化方法存在")
        else:
            print("❌ AI优化方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务处理器集成测试失败: {e}")
        return False

def test_frontend_integration():
    """测试前端集成"""
    print("\n📋 前端集成测试")
    print("-" * 30)
    
    try:
        # 检查JavaScript文件是否包含相关功能
        js_file_path = 'static/js/main.js'
        
        if os.path.exists(js_file_path):
            with open(js_file_path, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # 检查关键函数
            key_functions = [
                'displayOptimizationInfo',
                'displayComparisonPreview',
                'generateComparisonContent',
                'updateDownloadButtons'
            ]
            
            for func_name in key_functions:
                if func_name in js_content:
                    print(f"✓ JavaScript函数存在: {func_name}")
                else:
                    print(f"❌ JavaScript函数缺失: {func_name}")
        else:
            print("❌ JavaScript文件不存在")
        
        # 检查CSS文件
        css_file_path = 'static/css/style.css'
        
        if os.path.exists(css_file_path):
            with open(css_file_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # 检查关键样式
            key_styles = [
                '.optimization-info',
                '.subtitle-comparison',
                '.comparison-entry',
                '.optimized-text'
            ]
            
            for style_name in key_styles:
                if style_name in css_content:
                    print(f"✓ CSS样式存在: {style_name}")
                else:
                    print(f"❌ CSS样式缺失: {style_name}")
        else:
            print("❌ CSS文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("音视频转字幕应用 - DeepSeek AI字幕优化功能测试")
    print("=" * 50)
    
    tests = [
        ("DeepSeek模块导入测试", test_deepseek_module_import),
        ("DeepSeek服务创建测试", test_deepseek_service_creation),
        ("SRT解析测试", test_srt_parsing),
        ("优化提示词测试", test_optimization_prompt),
        ("优化结果解析测试", test_optimization_result_parsing),
        ("任务处理器集成测试", test_task_processor_integration),
        ("前端集成测试", test_frontend_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常：{e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有DeepSeek AI字幕优化功能测试通过！")
        print("📝 步骤五：DeepSeek AI字幕优化 - 完成")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
