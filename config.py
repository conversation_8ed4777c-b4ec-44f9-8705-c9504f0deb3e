# -*- coding: utf-8 -*-
"""
音视频转字幕应用配置文件
包含Flask基础配置、API密钥、文件上传限制等设置
"""

import os
import logging
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    TEMP_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
    LOG_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    
    # 文件大小限制 (500MB)
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024
    
    # 支持的音频格式
    ALLOWED_AUDIO_EXTENSIONS = {
        'mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'
    }
    
    # 支持的视频格式
    ALLOWED_VIDEO_EXTENSIONS = {
        'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', '3gp'
    }
    
    # 所有支持的文件格式
    ALLOWED_EXTENSIONS = ALLOWED_AUDIO_EXTENSIONS | ALLOWED_VIDEO_EXTENSIONS
    
    # API密钥配置
    DEEPSEEK_API_KEY = "***********************************"
    DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"
    
    # 腾讯云API配置
    TENCENT_SECRET_ID = "AKIDeMsBIDw51lIiwynQq6KYkpkU0KQK3e1B"
    TENCENT_SECRET_KEY = "2qTt9D8qMgqqdDapw471nQtDMK9qNly9"
    TENCENT_REGION = "ap-beijing"
    
    # 语音识别配置
    ASR_ENGINE_MODEL_TYPE = "16k_zh"  # 中文普通话
    ASR_CHANNEL_NUM = 1  # 单声道
    ASR_RES_TEXT_FORMAT = 0  # 基础识别结果
    
    # 文件清理配置
    FILE_CLEANUP_HOURS = 24  # 24小时后清理临时文件
    
    # 日志配置
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # 任务配置
    TASK_TIMEOUT = 3600  # 任务超时时间（秒）
    MAX_CONCURRENT_TASKS = 5  # 最大并发任务数
    
    # 音频处理配置
    AUDIO_SAMPLE_RATE = 16000  # 采样率
    AUDIO_CHANNELS = 1  # 声道数
    AUDIO_CHUNK_SIZE = 60  # 音频分片大小（秒）
    
    # SRT字幕配置
    SRT_MAX_CHARS_PER_LINE = 40  # 每行最大字符数
    SRT_MAX_LINES_PER_SUBTITLE = 2  # 每个字幕最大行数
    SRT_MIN_DURATION = 1.0  # 最小字幕持续时间（秒）
    SRT_MAX_DURATION = 7.0  # 最大字幕持续时间（秒）
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 确保必要的目录存在
        for folder in [Config.UPLOAD_FOLDER, Config.TEMP_FOLDER, Config.LOG_FOLDER]:
            if not os.path.exists(folder):
                os.makedirs(folder)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = logging.DEBUG

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = logging.WARNING
    
    # 生产环境下从环境变量获取敏感信息
    SECRET_KEY = os.environ.get('SECRET_KEY')
    DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY')
    TENCENT_SECRET_ID = os.environ.get('TENCENT_SECRET_ID')
    TENCENT_SECRET_KEY = os.environ.get('TENCENT_SECRET_KEY')

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    WTF_CSRF_ENABLED = False
    LOG_LEVEL = logging.DEBUG

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    return config[os.environ.get('FLASK_ENV', 'default')]
