/**
 * 音视频转字幕应用前端JavaScript
 * 处理文件上传、进度显示、状态更新等功能
 */

// 全局变量
let currentTaskId = null;
let progressInterval = null;
let timeInterval = null;
let appConfig = null;
let startTime = null;
let currentFile = null;
let isProcessing = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadAppConfig();
    setupKeyboardShortcuts();
});

/**
 * 初始化应用
 */
function initializeApp() {
    console.log('音视频转字幕应用初始化...');

    // 添加页面加载动画
    document.body.classList.add('fade-in');

    // 检查浏览器支持
    if (!window.File || !window.FileReader || !window.FormData) {
        showAlert('您的浏览器不支持文件上传功能，请升级浏览器', 'danger');
        return;
    }

    // 检查必要的API支持
    if (!window.fetch) {
        showAlert('您的浏览器不支持现代网络功能，请升级浏览器', 'danger');
        return;
    }

    // 初始化工具提示
    initializeTooltips();

    console.log('应用初始化完成');
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    // 如果Bootstrap的tooltip可用，初始化所有工具提示
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * 设置键盘快捷键
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl+O 或 Cmd+O 打开文件选择
        if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
            event.preventDefault();
            if (!isProcessing) {
                document.getElementById('fileInput').click();
            }
        }

        // Escape 键取消当前操作
        if (event.key === 'Escape') {
            if (isProcessing) {
                showConfirmDialog('确定要取消当前处理吗？', cancelProcessing);
            }
        }
    });
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    const selectFileBtn = document.getElementById('selectFileBtn');
    const clearFileBtn = document.getElementById('clearFileBtn');
    const startProcessBtn = document.getElementById('startProcessBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const processNewFileBtn = document.getElementById('processNewFile');

    // 文件选择事件
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }

    // 选择文件按钮
    if (selectFileBtn) {
        selectFileBtn.addEventListener('click', function() {
            if (!isProcessing) {
                fileInput.click();
            }
        });
    }

    // 清除文件按钮
    if (clearFileBtn) {
        clearFileBtn.addEventListener('click', clearSelectedFile);
    }

    // 开始处理按钮
    if (startProcessBtn) {
        startProcessBtn.addEventListener('click', startProcessing);
    }

    // 取消处理按钮
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            showConfirmDialog('确定要取消当前处理吗？', cancelProcessing);
        });
    }

    // 处理新文件按钮
    if (processNewFileBtn) {
        processNewFileBtn.addEventListener('click', resetForNewFile);
    }

    // 拖拽上传事件
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleFileDrop);

        // 点击上传区域选择文件
        uploadArea.addEventListener('click', function(event) {
            // 避免在文件预览区域点击时触发
            if (!event.target.closest('.file-preview') && !isProcessing) {
                fileInput.click();
            }
        });
    }

    // 下载按钮事件
    const downloadOriginal = document.getElementById('downloadOriginal');
    const downloadOptimized = document.getElementById('downloadOptimized');

    if (downloadOriginal) {
        downloadOriginal.addEventListener('click', function() {
            downloadFile('original');
        });
    }

    if (downloadOptimized) {
        downloadOptimized.addEventListener('click', function() {
            downloadFile('optimized');
        });
    }

    // AI优化开关事件
    const aiOptimizeSwitch = document.getElementById('aiOptimizeSwitch');
    if (aiOptimizeSwitch) {
        aiOptimizeSwitch.addEventListener('change', function() {
            const isEnabled = this.checked;
            console.log('AI优化', isEnabled ? '已启用' : '已禁用');
            // 可以在这里添加相关的UI更新
        });
    }

    // 页面离开前确认
    window.addEventListener('beforeunload', function(event) {
        if (isProcessing) {
            event.preventDefault();
            event.returnValue = '正在处理文件，确定要离开吗？';
            return event.returnValue;
        }
    });
}

/**
 * 加载应用配置
 */
async function loadAppConfig() {
    try {
        const response = await fetch('/api/config');
        if (response.ok) {
            appConfig = await response.json();
            console.log('应用配置加载完成:', appConfig);
        }
    } catch (error) {
        console.error('加载应用配置失败:', error);
    }
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length > 0 && !isProcessing) {
        selectFile(files[0]);
    }
}

/**
 * 处理拖拽悬停
 */
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();

    if (!isProcessing) {
        event.currentTarget.classList.add('dragover');
    }
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');
}

/**
 * 处理文件拖拽放置
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');

    if (!isProcessing) {
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            selectFile(files[0]);
        }
    }
}

/**
 * 选择文件
 */
function selectFile(file) {
    console.log('选择文件:', file.name);

    // 验证文件
    if (!validateFile(file)) {
        return;
    }

    // 保存当前文件
    currentFile = file;

    // 显示文件信息
    showFileInfo(file);

    // 显示文件预览
    showFilePreview(file);

    // 隐藏上传内容，显示预览
    toggleUploadView(false);
}

/**
 * 清除选择的文件
 */
function clearSelectedFile() {
    currentFile = null;

    // 清除文件输入
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.value = '';
    }

    // 隐藏预览，显示上传内容
    toggleUploadView(true);

    // 清除任何现有的文件信息
    const existingInfo = document.querySelector('.file-info');
    if (existingInfo) {
        existingInfo.remove();
    }
}

/**
 * 切换上传视图
 */
function toggleUploadView(showUpload) {
    const uploadContent = document.querySelector('.upload-content');
    const filePreview = document.getElementById('filePreview');
    const clearBtn = document.getElementById('clearFileBtn');

    if (showUpload) {
        uploadContent.style.display = 'block';
        filePreview.style.display = 'none';
        clearBtn.style.display = 'none';
    } else {
        uploadContent.style.display = 'none';
        filePreview.style.display = 'block';
        clearBtn.style.display = 'inline-block';
    }
}

/**
 * 开始处理
 */
function startProcessing() {
    if (!currentFile) {
        showAlert('请先选择一个文件', 'warning');
        return;
    }

    if (isProcessing) {
        showAlert('正在处理中，请稍候...', 'info');
        return;
    }

    // 确认开始处理
    const aiOptimize = document.getElementById('aiOptimizeSwitch').checked;
    const message = `确定要开始处理文件 "${currentFile.name}" 吗？${aiOptimize ? '\n将使用AI优化功能。' : ''}`;

    showConfirmDialog(message, function() {
        uploadAndProcessFile(currentFile);
    });
}

/**
 * 取消处理
 */
function cancelProcessing() {
    if (currentTaskId && isProcessing) {
        // 发送取消请求
        fetch(`/cancel/${currentTaskId}`, {
            method: 'POST'
        }).then(response => {
            if (response.ok) {
                showAlert('处理已取消', 'info');
            }
        }).catch(error => {
            console.error('取消请求失败:', error);
        });

        // 重置状态
        resetProcessingState();
    }
}

/**
 * 重置为新文件
 */
function resetForNewFile() {
    // 重置所有状态
    resetProcessingState();
    clearSelectedFile();

    // 隐藏进度和结果卡片
    hideProgress();
    hideResult();

    // 显示成功消息
    showAlert('已重置，可以处理新文件了', 'success');
}

/**
 * 验证文件
 */
function validateFile(file) {
    // 检查文件是否存在
    if (!file) {
        showAlert('请选择一个文件', 'warning');
        return false;
    }

    // 检查文件大小
    const maxSize = appConfig ? appConfig.max_file_size : 500 * 1024 * 1024; // 默认500MB
    if (file.size > maxSize) {
        const maxSizeMB = Math.round(maxSize / 1024 / 1024);
        showAlert(`文件大小超过限制（最大${maxSizeMB}MB）`, 'danger');
        return false;
    }

    // 检查文件大小不能为0
    if (file.size === 0) {
        showAlert('文件大小不能为0', 'danger');
        return false;
    }

    // 检查文件类型
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.split('.').pop();

    // 默认支持的扩展名
    const defaultExtensions = ['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma', 'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', '3gp'];
    const allowedExtensions = appConfig ? appConfig.allowed_extensions : defaultExtensions;

    if (!allowedExtensions.includes(fileExtension)) {
        showAlert(`不支持的文件格式。支持的格式：${allowedExtensions.join(', ')}`, 'danger');
        return false;
    }

    // 检查文件名长度
    if (file.name.length > 255) {
        showAlert('文件名过长，请重命名后再上传', 'danger');
        return false;
    }

    return true;
}

/**
 * 显示文件预览
 */
function showFilePreview(file) {
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileType = document.getElementById('fileType');
    const uploadProgress = document.getElementById('uploadProgress');

    if (fileName) fileName.textContent = file.name;
    if (fileSize) fileSize.textContent = formatFileSize(file.size);
    if (fileType) fileType.textContent = getFileTypeDescription(file);
    if (uploadProgress) uploadProgress.style.width = '0%';

    // 更新文件图标
    updateFileIcon(file);
}

/**
 * 显示文件信息（保留原有功能）
 */
function showFileInfo(file) {
    const fileSize = formatFileSize(file.size);
    const fileType = getFileTypeDescription(file);
    const duration = estimateAudioDuration(file);

    const fileInfoHtml = `
        <div class="file-info animate__animated animate__fadeInUp">
            <h6><i class="fas fa-info-circle me-2"></i>文件详情</h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>文件名：</strong>${file.name}</p>
                    <p><strong>文件大小：</strong>${fileSize}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>文件类型：</strong>${fileType}</p>
                    <p><strong>预估时长：</strong>${duration}</p>
                </div>
            </div>
        </div>
    `;

    // 在上传区域后插入文件信息
    const uploadArea = document.getElementById('uploadArea');
    const existingInfo = document.querySelector('.file-info');

    if (existingInfo) {
        existingInfo.remove();
    }

    uploadArea.insertAdjacentHTML('afterend', fileInfoHtml);
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件类型描述
 */
function getFileTypeDescription(file) {
    const extension = file.name.split('.').pop().toLowerCase();
    const audioFormats = ['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'];
    const videoFormats = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', '3gp'];

    if (audioFormats.includes(extension)) {
        return `音频文件 (${extension.toUpperCase()})`;
    } else if (videoFormats.includes(extension)) {
        return `视频文件 (${extension.toUpperCase()})`;
    } else {
        return `${extension.toUpperCase()} 文件`;
    }
}

/**
 * 预估音频时长
 */
function estimateAudioDuration(file) {
    // 这是一个简单的估算，实际时长需要在服务器端获取
    const avgBitrate = 128; // kbps
    const estimatedSeconds = (file.size * 8) / (avgBitrate * 1000);

    if (estimatedSeconds < 60) {
        return `约 ${Math.round(estimatedSeconds)} 秒`;
    } else if (estimatedSeconds < 3600) {
        const minutes = Math.round(estimatedSeconds / 60);
        return `约 ${minutes} 分钟`;
    } else {
        const hours = Math.floor(estimatedSeconds / 3600);
        const minutes = Math.round((estimatedSeconds % 3600) / 60);
        return `约 ${hours} 小时 ${minutes} 分钟`;
    }
}

/**
 * 更新文件图标
 */
function updateFileIcon(file) {
    const extension = file.name.split('.').pop().toLowerCase();
    const audioFormats = ['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'];
    const videoFormats = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', '3gp'];

    const iconElement = document.querySelector('.file-info-card i');
    if (iconElement) {
        if (audioFormats.includes(extension)) {
            iconElement.className = 'fas fa-file-audio fa-3x text-success';
        } else if (videoFormats.includes(extension)) {
            iconElement.className = 'fas fa-file-video fa-3x text-primary';
        } else {
            iconElement.className = 'fas fa-file fa-3x text-secondary';
        }
    }
}

/**
 * 上传并处理文件
 */
async function uploadAndProcessFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    // 获取AI优化选项
    const aiOptimize = document.getElementById('aiOptimizeSwitch').checked;
    formData.append('ai_optimize', aiOptimize);

    try {
        // 设置处理状态
        isProcessing = true;
        updateProcessingUI(true);

        // 显示进度
        showProgress('正在上传文件...', 5);
        updateStepIndicator(1, 'active');

        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            currentTaskId = result.task_id;
            showAlert('文件上传成功，开始处理...', 'success');

            // 开始处理
            await startProcessing(currentTaskId);

        } else {
            showAlert(result.error || '文件上传失败', 'danger');
            resetProcessingState();
        }
    } catch (error) {
        console.error('上传错误:', error);
        showAlert('网络错误，请检查网络连接', 'danger');
        resetProcessingState();
    }
}

/**
 * 开始处理任务
 */
async function startProcessing(taskId) {
    try {
        const response = await fetch(`/process/${taskId}`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            // 开始监控进度
            startProgressMonitoring();
            startTimeTracking();
        } else {
            showAlert(result.error || '启动处理失败', 'danger');
            resetProcessingState();
        }

    } catch (error) {
        console.error('启动处理错误:', error);
        showAlert('启动处理失败', 'danger');
        resetProcessingState();
    }
}

/**
 * 显示进度
 */
function showProgress(message, percentage = 0) {
    const progressCard = document.getElementById('progressCard');
    const progressBar = progressCard.querySelector('.progress-bar');
    const statusText = document.getElementById('statusText');
    
    progressCard.style.display = 'block';
    progressCard.classList.add('slide-up');
    progressBar.style.width = percentage + '%';
    statusText.textContent = message;
    
    // 隐藏结果卡片
    const resultCard = document.getElementById('resultCard');
    resultCard.style.display = 'none';
}

/**
 * 隐藏进度
 */
function hideProgress() {
    const progressCard = document.getElementById('progressCard');
    progressCard.style.display = 'none';
    
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

/**
 * 开始进度监控
 */
function startProgressMonitoring() {
    if (!currentTaskId) return;
    
    progressInterval = setInterval(async () => {
        try {
            const response = await fetch(`/progress/${currentTaskId}`);
            const result = await response.json();
            
            if (result.success) {
                updateProgress(result.progress, result.status);
                
                if (result.completed) {
                    clearInterval(progressInterval);
                    showResult(result);
                }
            } else {
                clearInterval(progressInterval);
                showAlert(result.error || '处理失败', 'danger');
                hideProgress();
            }
        } catch (error) {
            console.error('进度查询错误:', error);
        }
    }, 2000); // 每2秒查询一次
}

/**
 * 更新进度
 */
function updateProgress(percentage, status) {
    const progressBar = document.getElementById('mainProgressBar');
    const progressPercentage = document.getElementById('progressPercentage');
    const statusText = document.getElementById('statusText');
    const detailStatus = document.getElementById('detailStatus');

    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }

    if (progressPercentage) {
        progressPercentage.textContent = percentage + '%';
    }

    if (statusText) {
        statusText.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${status}`;
    }

    // 更新步骤指示器
    updateStepIndicatorByProgress(percentage);
}

/**
 * 根据进度更新步骤指示器
 */
function updateStepIndicatorByProgress(percentage) {
    if (percentage >= 5 && percentage < 30) {
        updateStepIndicator(1, 'active');
    } else if (percentage >= 30 && percentage < 40) {
        updateStepIndicator(1, 'completed');
        updateStepIndicator(2, 'active');
    } else if (percentage >= 40 && percentage < 80) {
        updateStepIndicator(1, 'completed');
        updateStepIndicator(2, 'completed');
        updateStepIndicator(3, 'active');
    } else if (percentage >= 80) {
        updateStepIndicator(1, 'completed');
        updateStepIndicator(2, 'completed');
        updateStepIndicator(3, 'completed');
        updateStepIndicator(4, 'active');
    }
}

/**
 * 更新步骤指示器
 */
function updateStepIndicator(stepNumber, status) {
    const stepElement = document.getElementById(`step${stepNumber}`);
    if (stepElement) {
        // 清除所有状态类
        stepElement.classList.remove('active', 'completed');

        // 添加新状态类
        if (status === 'active' || status === 'completed') {
            stepElement.classList.add(status);
        }
    }
}

/**
 * 开始时间跟踪
 */
function startTimeTracking() {
    startTime = new Date();

    timeInterval = setInterval(() => {
        if (startTime) {
            const elapsed = Math.floor((new Date() - startTime) / 1000);
            const timeElapsedElement = document.getElementById('timeElapsed');

            if (timeElapsedElement) {
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                timeElapsedElement.textContent = `已用时: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }
    }, 1000);
}

/**
 * 停止时间跟踪
 */
function stopTimeTracking() {
    if (timeInterval) {
        clearInterval(timeInterval);
        timeInterval = null;
    }
}

/**
 * 更新处理UI状态
 */
function updateProcessingUI(processing) {
    const selectFileBtn = document.getElementById('selectFileBtn');
    const startProcessBtn = document.getElementById('startProcessBtn');
    const clearFileBtn = document.getElementById('clearFileBtn');
    const fileInput = document.getElementById('fileInput');

    if (selectFileBtn) selectFileBtn.disabled = processing;
    if (startProcessBtn) startProcessBtn.disabled = processing;
    if (clearFileBtn) clearFileBtn.disabled = processing;
    if (fileInput) fileInput.disabled = processing;
}

/**
 * 重置处理状态
 */
function resetProcessingState() {
    isProcessing = false;
    currentTaskId = null;
    startTime = null;

    updateProcessingUI(false);
    stopTimeTracking();
    hideProgress();

    // 重置步骤指示器
    for (let i = 1; i <= 4; i++) {
        updateStepIndicator(i, '');
    }
}

/**
 * 显示结果
 */
function showResult(result) {
    hideProgress();
    
    const resultCard = document.getElementById('resultCard');
    resultCard.style.display = 'block';
    resultCard.classList.add('slide-up');
    
    showAlert('转换完成！您可以下载字幕文件了。', 'success');
}

/**
 * 下载文件
 */
function downloadFile(type) {
    if (!currentTaskId) {
        showAlert('没有可下载的文件', 'danger');
        return;
    }
    
    const url = `/download/${currentTaskId}?type=${type}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 显示确认对话框
 */
function showConfirmDialog(message, onConfirm) {
    const modalBody = document.getElementById('confirmModalBody');
    const confirmBtn = document.getElementById('confirmModalBtn');

    if (modalBody) {
        modalBody.textContent = message;
    }

    // 移除之前的事件监听器
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
        if (modal) {
            modal.hide();
        }
        if (onConfirm) {
            onConfirm();
        }
    });

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
}

/**
 * 隐藏结果卡片
 */
function hideResult() {
    const resultCard = document.getElementById('resultCard');
    if (resultCard) {
        resultCard.style.display = 'none';
    }
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 移除现有的提示
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 在提示容器中插入提示
    const alertContainer = document.getElementById('alertContainer');
    if (alertContainer) {
        alertContainer.innerHTML = alertHtml;
    } else {
        // 备用方案：在容器顶部插入
        const container = document.querySelector('.container');
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }

    // 自动隐藏提示（除了错误提示）
    if (type !== 'danger') {
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.classList.add('animate__fadeOutUp');
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        }, 5000);
    }
}

/**
 * 获取提示图标
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * 格式化时间
 */
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * 格式化文件大小
 */
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 检查网络连接
 */
function checkNetworkConnection() {
    return navigator.onLine;
}

/**
 * 页面可见性变化处理
 */
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停某些操作
        console.log('页面隐藏');
    } else {
        // 页面显示时恢复操作
        console.log('页面显示');
        if (isProcessing && currentTaskId) {
            // 重新检查任务状态
            checkTaskStatus();
        }
    }
});

/**
 * 检查任务状态
 */
async function checkTaskStatus() {
    if (!currentTaskId) return;

    try {
        const response = await fetch(`/progress/${currentTaskId}`);
        const result = await response.json();

        if (result.success) {
            updateProgress(result.progress, result.message);

            if (result.completed) {
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }

                if (result.status === 'completed') {
                    showResult(result);
                } else {
                    showAlert(result.error || '处理失败', 'danger');
                    resetProcessingState();
                }
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}
