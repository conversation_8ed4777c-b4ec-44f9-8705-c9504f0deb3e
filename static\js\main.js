/**
 * 音视频转字幕应用前端JavaScript
 * 处理文件上传、进度显示、状态更新等功能
 */

// 全局变量
let currentTaskId = null;
let progressInterval = null;
let appConfig = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadAppConfig();
});

/**
 * 初始化应用
 */
function initializeApp() {
    console.log('音视频转字幕应用初始化...');
    
    // 添加页面加载动画
    document.body.classList.add('fade-in');
    
    // 检查浏览器支持
    if (!window.File || !window.FileReader || !window.FormData) {
        showAlert('您的浏览器不支持文件上传功能，请升级浏览器', 'danger');
        return;
    }
    
    console.log('应用初始化完成');
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.querySelector('.upload-area');
    
    // 文件选择事件
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
    
    // 拖拽上传事件
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleFileDrop);
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });
    }
    
    // 下载按钮事件
    const downloadOriginal = document.getElementById('downloadOriginal');
    const downloadOptimized = document.getElementById('downloadOptimized');
    
    if (downloadOriginal) {
        downloadOriginal.addEventListener('click', function() {
            downloadFile('original');
        });
    }
    
    if (downloadOptimized) {
        downloadOptimized.addEventListener('click', function() {
            downloadFile('optimized');
        });
    }
}

/**
 * 加载应用配置
 */
async function loadAppConfig() {
    try {
        const response = await fetch('/api/config');
        if (response.ok) {
            appConfig = await response.json();
            console.log('应用配置加载完成:', appConfig);
        }
    } catch (error) {
        console.error('加载应用配置失败:', error);
    }
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

/**
 * 处理拖拽悬停
 */
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.add('dragover');
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');
}

/**
 * 处理文件拖拽放置
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

/**
 * 处理文件
 */
function processFile(file) {
    console.log('处理文件:', file.name);
    
    // 验证文件
    if (!validateFile(file)) {
        return;
    }
    
    // 显示文件信息
    showFileInfo(file);
    
    // 开始上传
    uploadFile(file);
}

/**
 * 验证文件
 */
function validateFile(file) {
    // 检查文件大小
    if (appConfig && file.size > appConfig.max_file_size) {
        showAlert(`文件大小超过限制（最大${Math.round(appConfig.max_file_size / 1024 / 1024)}MB）`, 'danger');
        return false;
    }
    
    // 检查文件类型
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.split('.').pop();
    
    if (appConfig && !appConfig.allowed_extensions.includes(fileExtension)) {
        showAlert(`不支持的文件格式。支持的格式：${appConfig.allowed_extensions.join(', ')}`, 'danger');
        return false;
    }
    
    return true;
}

/**
 * 显示文件信息
 */
function showFileInfo(file) {
    const fileSize = (file.size / 1024 / 1024).toFixed(2);
    const fileType = file.type || '未知';
    
    const fileInfoHtml = `
        <div class="file-info">
            <h6><i class="fas fa-file me-2"></i>文件信息</h6>
            <p><strong>文件名：</strong>${file.name}</p>
            <p><strong>文件大小：</strong>${fileSize} MB</p>
            <p><strong>文件类型：</strong>${fileType}</p>
        </div>
    `;
    
    // 在上传区域后插入文件信息
    const uploadArea = document.querySelector('.upload-area');
    const existingInfo = document.querySelector('.file-info');
    
    if (existingInfo) {
        existingInfo.remove();
    }
    
    uploadArea.insertAdjacentHTML('afterend', fileInfoHtml);
}

/**
 * 上传文件
 */
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showProgress('正在上传文件...', 10);
        
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentTaskId = result.task_id;
            showAlert('文件上传成功，开始处理...', 'success');
            startProgressMonitoring();
        } else {
            showAlert(result.error || '文件上传失败', 'danger');
            hideProgress();
        }
    } catch (error) {
        console.error('上传错误:', error);
        showAlert('网络错误，请检查网络连接', 'danger');
        hideProgress();
    }
}

/**
 * 显示进度
 */
function showProgress(message, percentage = 0) {
    const progressCard = document.getElementById('progressCard');
    const progressBar = progressCard.querySelector('.progress-bar');
    const statusText = document.getElementById('statusText');
    
    progressCard.style.display = 'block';
    progressCard.classList.add('slide-up');
    progressBar.style.width = percentage + '%';
    statusText.textContent = message;
    
    // 隐藏结果卡片
    const resultCard = document.getElementById('resultCard');
    resultCard.style.display = 'none';
}

/**
 * 隐藏进度
 */
function hideProgress() {
    const progressCard = document.getElementById('progressCard');
    progressCard.style.display = 'none';
    
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

/**
 * 开始进度监控
 */
function startProgressMonitoring() {
    if (!currentTaskId) return;
    
    progressInterval = setInterval(async () => {
        try {
            const response = await fetch(`/progress/${currentTaskId}`);
            const result = await response.json();
            
            if (result.success) {
                updateProgress(result.progress, result.status);
                
                if (result.completed) {
                    clearInterval(progressInterval);
                    showResult(result);
                }
            } else {
                clearInterval(progressInterval);
                showAlert(result.error || '处理失败', 'danger');
                hideProgress();
            }
        } catch (error) {
            console.error('进度查询错误:', error);
        }
    }, 2000); // 每2秒查询一次
}

/**
 * 更新进度
 */
function updateProgress(percentage, status) {
    const progressBar = document.querySelector('.progress-bar');
    const statusText = document.getElementById('statusText');
    
    progressBar.style.width = percentage + '%';
    statusText.innerHTML = `<span class="loading-spinner me-2"></span>${status}`;
}

/**
 * 显示结果
 */
function showResult(result) {
    hideProgress();
    
    const resultCard = document.getElementById('resultCard');
    resultCard.style.display = 'block';
    resultCard.classList.add('slide-up');
    
    showAlert('转换完成！您可以下载字幕文件了。', 'success');
}

/**
 * 下载文件
 */
function downloadFile(type) {
    if (!currentTaskId) {
        showAlert('没有可下载的文件', 'danger');
        return;
    }
    
    const url = `/download/${currentTaskId}?type=${type}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 移除现有的提示
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在容器顶部插入提示
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 自动隐藏提示（除了错误提示）
    if (type !== 'danger') {
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
