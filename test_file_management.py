# -*- coding: utf-8 -*-
"""
下载功能和文件管理测试脚本
测试文件下载、文件管理、清理等功能
"""

import os
import sys
import tempfile
import json
from pathlib import Path

def test_file_manager_import():
    """测试文件管理器模块导入"""
    print("📋 文件管理器模块导入测试")
    print("-" * 30)
    
    try:
        from file_manager import FileManager, create_file_manager
        print("✓ 文件管理器模块导入成功")
        
        # 测试模块类
        if hasattr(FileManager, 'get_file_info'):
            print("✓ 文件信息获取方法存在")
        else:
            print("❌ 文件信息获取方法不存在")
        
        if hasattr(FileManager, 'cleanup_old_files'):
            print("✓ 文件清理方法存在")
        else:
            print("❌ 文件清理方法不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 文件管理器模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件管理器模块测试失败: {e}")
        return False

def test_file_manager_creation():
    """测试文件管理器创建"""
    print("\n📋 文件管理器创建测试")
    print("-" * 30)
    
    try:
        from app import app
        from file_manager import create_file_manager
        
        with app.app_context():
            config = app.config
            
            # 检查配置
            required_configs = ['UPLOAD_FOLDER', 'TEMP_FOLDER', 'LOG_FOLDER']
            for config_key in required_configs:
                if config.get(config_key):
                    print(f"✓ {config_key} 已配置")
                else:
                    print(f"❌ {config_key} 未配置")
            
            try:
                # 尝试创建文件管理器
                file_manager = create_file_manager(config)
                print("✓ 文件管理器创建成功")
                
                # 检查目录是否存在
                if file_manager.upload_dir.exists():
                    print("✓ 上传目录存在")
                else:
                    print("⚠️ 上传目录不存在")
                
                if file_manager.temp_dir.exists():
                    print("✓ 临时目录存在")
                else:
                    print("⚠️ 临时目录不存在")
                    
            except Exception as e:
                print(f"⚠️ 文件管理器创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件管理器创建测试失败: {e}")
        return False

def test_file_info_functions():
    """测试文件信息功能"""
    print("\n📋 文件信息功能测试")
    print("-" * 30)
    
    try:
        from file_manager import FileManager
        
        # 创建临时文件管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            upload_dir = os.path.join(temp_dir, 'upload')
            temp_folder = os.path.join(temp_dir, 'temp')
            log_dir = os.path.join(temp_dir, 'log')
            
            file_manager = FileManager(upload_dir, temp_folder, log_dir)
            
            # 创建测试文件
            test_file = os.path.join(upload_dir, 'test.txt')
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write('这是一个测试文件')
            
            # 测试文件信息获取
            file_info = file_manager.get_file_info(test_file)
            
            if file_info:
                print("✓ 文件信息获取成功")
                
                required_fields = ['path', 'name', 'size', 'created_time', 'hash']
                for field in required_fields:
                    if field in file_info:
                        print(f"  ✓ 包含字段: {field}")
                    else:
                        print(f"  ❌ 缺少字段: {field}")
            else:
                print("❌ 文件信息获取失败")
            
            # 测试不存在文件
            nonexistent_info = file_manager.get_file_info('/nonexistent/file.txt')
            if nonexistent_info is None:
                print("✓ 不存在文件处理正确")
            else:
                print("❌ 不存在文件处理错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件信息功能测试失败: {e}")
        return False

def test_file_security_validation():
    """测试文件安全验证"""
    print("\n📋 文件安全验证测试")
    print("-" * 30)
    
    try:
        from file_manager import FileManager
        
        # 创建临时文件管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            upload_dir = os.path.join(temp_dir, 'upload')
            temp_folder = os.path.join(temp_dir, 'temp')
            log_dir = os.path.join(temp_dir, 'log')
            
            file_manager = FileManager(upload_dir, temp_folder, log_dir)
            
            # 创建测试文件
            test_file = os.path.join(upload_dir, 'test.mp3')
            with open(test_file, 'w') as f:
                f.write('test content')
            
            # 测试安全文件
            is_safe, message = file_manager.validate_file_security(test_file)
            if is_safe:
                print("✓ 安全文件验证通过")
            else:
                print(f"⚠️ 安全文件验证失败: {message}")
            
            # 测试不存在文件
            is_safe, message = file_manager.validate_file_security('/nonexistent/file.mp3')
            if not is_safe and '不存在' in message:
                print("✓ 不存在文件验证正确")
            else:
                print(f"❌ 不存在文件验证错误: {message}")
            
            # 测试不支持的文件类型
            bad_file = os.path.join(upload_dir, 'test.exe')
            with open(bad_file, 'w') as f:
                f.write('bad content')
            
            is_safe, message = file_manager.validate_file_security(bad_file)
            if not is_safe and '不支持' in message:
                print("✓ 不支持文件类型验证正确")
            else:
                print(f"❌ 不支持文件类型验证错误: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件安全验证测试失败: {e}")
        return False

def test_download_routes():
    """测试下载路由"""
    print("\n📋 下载路由测试")
    print("-" * 30)
    
    try:
        from app import app, create_task, update_task
        
        with app.test_client() as client:
            # 创建一个测试任务
            file_info = {
                'original_name': 'test.mp3',
                'unique_name': 'test_123.mp3',
                'file_path': '/tmp/test_123.mp3',
                'size': 1024000,
                'file_type': 'audio'
            }
            
            task_id = create_task('test_task', file_info)
            
            # 测试未完成任务的下载
            response = client.get(f'/download/{task_id}')
            if response.status_code == 400:
                print("✓ 未完成任务下载处理正确")
            else:
                print(f"⚠️ 未完成任务下载响应: {response.status_code}")
            
            # 测试文件统计接口
            response = client.get('/files/stats')
            if response.status_code == 200:
                print("✓ 文件统计接口正常")
                
                try:
                    data = json.loads(response.get_data(as_text=True))
                    if data['success'] and 'stats' in data:
                        print("  ✓ 统计数据格式正确")
                    else:
                        print("  ⚠️ 统计数据格式可能不正确")
                except:
                    print("  ⚠️ 统计数据解析失败")
            else:
                print(f"❌ 文件统计接口异常: {response.status_code}")
            
            # 测试文件清理接口
            response = client.post('/files/cleanup', 
                                 json={'max_age_hours': 1},
                                 content_type='application/json')
            if response.status_code == 200:
                print("✓ 文件清理接口正常")
            else:
                print(f"❌ 文件清理接口异常: {response.status_code}")
            
            # 测试任务文件信息接口
            response = client.get(f'/files/info/{task_id}')
            if response.status_code == 200:
                print("✓ 任务文件信息接口正常")
            else:
                print(f"❌ 任务文件信息接口异常: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载路由测试失败: {e}")
        return False

def test_file_cleanup():
    """测试文件清理功能"""
    print("\n📋 文件清理功能测试")
    print("-" * 30)
    
    try:
        from file_manager import FileManager
        import time
        
        # 创建临时文件管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            upload_dir = os.path.join(temp_dir, 'upload')
            temp_folder = os.path.join(temp_dir, 'temp')
            log_dir = os.path.join(temp_dir, 'log')
            
            file_manager = FileManager(upload_dir, temp_folder, log_dir)
            
            # 创建一些测试文件
            test_files = []
            for i in range(3):
                test_file = os.path.join(upload_dir, f'test_{i}.txt')
                with open(test_file, 'w') as f:
                    f.write(f'test content {i}')
                test_files.append(test_file)
            
            # 修改文件时间（模拟旧文件）
            old_time = time.time() - 25 * 3600  # 25小时前
            for test_file in test_files[:2]:  # 前两个文件设为旧文件
                os.utime(test_file, (old_time, old_time))
            
            # 执行清理
            cleanup_stats = file_manager.cleanup_old_files(24)
            
            if 'upload_files_removed' in cleanup_stats:
                removed_count = cleanup_stats['upload_files_removed']
                print(f"✓ 文件清理执行成功，删除了 {removed_count} 个文件")
                
                if removed_count == 2:
                    print("✓ 清理数量正确")
                else:
                    print(f"⚠️ 清理数量可能不正确，期望2个，实际{removed_count}个")
            else:
                print("❌ 文件清理结果格式错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件清理功能测试失败: {e}")
        return False

def test_frontend_integration():
    """测试前端集成"""
    print("\n📋 前端集成测试")
    print("-" * 30)
    
    try:
        # 检查JavaScript文件是否包含相关功能
        js_file_path = 'static/js/main.js'
        
        if os.path.exists(js_file_path):
            with open(js_file_path, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # 检查关键函数
            key_functions = [
                'showFileStats',
                'performFileCleanup',
                'downloadAllFiles',
                'getTaskFilesInfo',
                'displayFileStatsModal'
            ]
            
            for func_name in key_functions:
                if func_name in js_content:
                    print(f"✓ JavaScript函数存在: {func_name}")
                else:
                    print(f"❌ JavaScript函数缺失: {func_name}")
        else:
            print("❌ JavaScript文件不存在")
        
        # 检查HTML模板
        html_file_path = 'templates/index.html'
        
        if os.path.exists(html_file_path):
            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 检查关键元素
            key_elements = [
                'downloadAll',
                'fileStatsBtn',
                'cleanupBtn',
                'downloadAllFiles()'
            ]
            
            for element in key_elements:
                if element in html_content:
                    print(f"✓ HTML元素存在: {element}")
                else:
                    print(f"❌ HTML元素缺失: {element}")
        else:
            print("❌ HTML模板文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("音视频转字幕应用 - 下载功能和文件管理测试")
    print("=" * 50)
    
    tests = [
        ("文件管理器模块导入测试", test_file_manager_import),
        ("文件管理器创建测试", test_file_manager_creation),
        ("文件信息功能测试", test_file_info_functions),
        ("文件安全验证测试", test_file_security_validation),
        ("下载路由测试", test_download_routes),
        ("文件清理功能测试", test_file_cleanup),
        ("前端集成测试", test_frontend_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常：{e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有下载功能和文件管理测试通过！")
        print("📝 步骤六：下载功能和文件管理 - 完成")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
