# -*- coding: utf-8 -*-
"""
任务处理器模块
负责异步处理音视频转字幕任务
"""

import os
import logging
import threading
import time
from datetime import datetime, timedelta

from audio_processor import process_media_file
from tencent_asr import create_asr_service
from deepseek_ai import create_deepseek_service

class TaskProcessor:
    """任务处理器"""
    
    def __init__(self, app, tasks_dict, task_lock):
        self.app = app
        self.tasks = tasks_dict
        self.task_lock = task_lock
        self.logger = logging.getLogger(__name__)
        self.processing_threads = {}
        
    def start_processing(self, task_id):
        """开始处理任务"""
        try:
            task = self.get_task(task_id)
            if not task:
                self.logger.error(f"任务不存在: {task_id}")
                return False
            
            if task['status'] != 'created':
                self.logger.warning(f"任务状态不正确: {task_id}, 状态: {task['status']}")
                return False
            
            # 创建处理线程
            thread = threading.Thread(
                target=self._process_task,
                args=(task_id,),
                daemon=True
            )
            
            self.processing_threads[task_id] = thread
            thread.start()
            
            self.logger.info(f"开始处理任务: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动任务处理失败: {str(e)}")
            return False
    
    def _process_task(self, task_id):
        """处理任务的主函数"""
        try:
            # 更新任务状态
            self.update_task(task_id, 
                           status='processing', 
                           progress=5, 
                           message='开始处理文件...')
            
            task = self.get_task(task_id)
            if not task:
                return
            
            file_info = task['file_info']
            file_path = file_info['file_path']
            options = task.get('options', {})
            
            # 步骤1: 文件预处理
            self.update_task(task_id, 
                           progress=10, 
                           message='正在预处理文件...')
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise Exception("源文件不存在")
            
            # 步骤2: 音频处理
            self.update_task(task_id, 
                           progress=20, 
                           message='正在处理音频...')
            
            # 处理媒体文件
            with self.app.app_context():
                temp_dir = self.app.config['TEMP_FOLDER']
                
            media_result = process_media_file(file_path, temp_dir)
            audio_path = media_result['audio_path']
            audio_info = media_result['audio_info']
            processor = media_result['processor']
            
            # 更新任务信息
            self.update_task(task_id, 
                           progress=30, 
                           message='音频处理完成，准备语音识别...')
            
            # 步骤3: 语音识别
            self.update_task(task_id,
                           progress=40,
                           message='正在进行语音识别...')

            # 执行语音识别
            recognition_result = self._perform_speech_recognition(task_id, audio_path, audio_info)
            
            # 步骤4: AI优化（如果启用）
            if options.get('ai_optimize', True):
                self.update_task(task_id,
                               progress=80,
                               message='正在进行AI优化...')

                # 执行AI优化
                recognition_result = self._perform_ai_optimization(task_id, recognition_result)
            
            # 步骤5: 完成处理
            self.update_task(task_id, 
                           progress=100, 
                           status='completed',
                           message='处理完成')
            
            # 生成结果文件路径
            original_srt_path = self._save_recognition_result(
                task_id, recognition_result, 'original'
            )

            optimized_srt_path = None
            if options.get('ai_optimize', True) and recognition_result.get('optimized_srt_content'):
                optimized_srt_path = self._save_recognition_result(
                    task_id, recognition_result, 'optimized'
                )

            # 生成结果
            result = {
                'audio_info': audio_info,
                'original_subtitle_path': original_srt_path,
                'optimized_subtitle_path': optimized_srt_path,
                'processing_time': (datetime.now() - task['created_time']).total_seconds(),
                'subtitle_count': recognition_result.get('subtitle_count', 0),
                'duration': audio_info.get('duration', 0),
                'recognition_text': recognition_result.get('text', ''),
                'recognition_method': recognition_result.get('method', 'unknown')
            }
            
            self.update_task(task_id, result=result)
            
            # 清理临时文件
            try:
                if audio_path != file_path:  # 如果是转换后的音频文件
                    processor.cleanup_temp_files([audio_path])
            except:
                pass
            
            self.logger.info(f"任务处理完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"任务处理失败 {task_id}: {str(e)}")
            self.update_task(task_id, 
                           status='failed', 
                           error=str(e),
                           message=f'处理失败: {str(e)}')
        finally:
            # 清理线程记录
            if task_id in self.processing_threads:
                del self.processing_threads[task_id]
    
    def _perform_speech_recognition(self, task_id, audio_path, audio_info):
        """执行语音识别"""
        try:
            # 创建ASR服务
            with self.app.app_context():
                asr_service = create_asr_service(self.app.config)

            # 定义进度回调函数
            def progress_callback(message):
                task = self.get_task(task_id)
                if task and task['status'] != 'cancelled':
                    # 根据消息更新进度
                    if '提交' in message:
                        progress = 45
                    elif '进行中' in message:
                        progress = min(65, 45 + int(time.time()) % 20)
                    else:
                        progress = 60

                    self.update_task(task_id,
                                   progress=progress,
                                   message=message)

            # 执行语音识别
            self.logger.info(f"开始语音识别: {audio_path}")
            recognition_result = asr_service.recognize_audio_file(
                audio_path,
                callback=progress_callback
            )

            if not recognition_result.get('success'):
                raise Exception("语音识别失败")

            # 统计字幕条数
            srt_content = recognition_result.get('srt_content', '')
            subtitle_count = len([line for line in srt_content.split('\n') if line.strip().isdigit()])

            recognition_result['subtitle_count'] = subtitle_count

            self.logger.info(f"语音识别完成: {task_id}, 字幕条数: {subtitle_count}")

            return recognition_result

        except Exception as e:
            self.logger.error(f"语音识别失败 {task_id}: {str(e)}")
            raise Exception(f"语音识别失败: {str(e)}")

    def _save_recognition_result(self, task_id, recognition_result, result_type):
        """保存识别结果为SRT文件"""
        try:
            with self.app.app_context():
                temp_dir = self.app.config['TEMP_FOLDER']

            # 确定SRT内容
            if result_type == 'optimized':
                srt_content = recognition_result.get('optimized_srt_content')
            else:
                srt_content = recognition_result.get('srt_content')

            if not srt_content:
                return None

            # 生成文件名
            filename = f"{task_id}_{result_type}.srt"
            file_path = os.path.join(temp_dir, filename)

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            self.logger.info(f"SRT文件保存成功: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"保存SRT文件失败: {str(e)}")
            return None
    
    def _perform_ai_optimization(self, task_id, recognition_result):
        """执行AI优化"""
        try:
            # 检查是否有字幕内容需要优化
            srt_content = recognition_result.get('srt_content')
            if not srt_content:
                self.logger.warning(f"没有字幕内容可供优化: {task_id}")
                return recognition_result

            # 创建DeepSeek AI服务
            with self.app.app_context():
                deepseek_service = create_deepseek_service(self.app.config)

            # 定义进度回调函数
            def progress_callback(message):
                task = self.get_task(task_id)
                if task and task['status'] != 'cancelled':
                    # 根据消息更新进度
                    if '解析' in message:
                        progress = 82
                    elif '优化进行中' in message:
                        progress = min(92, 82 + int(time.time()) % 10)
                    elif '生成' in message:
                        progress = 95
                    else:
                        progress = 85

                    self.update_task(task_id,
                                   progress=progress,
                                   message=message)

            # 执行AI优化
            self.logger.info(f"开始AI优化: {task_id}")
            optimization_result = deepseek_service.optimize_subtitle_text(
                srt_content,
                callback=progress_callback
            )

            if optimization_result.get('success'):
                # 更新识别结果
                recognition_result['optimized_srt_content'] = optimization_result['optimized_srt']
                recognition_result['optimization_summary'] = optimization_result['optimization_summary']

                self.logger.info(f"AI优化完成: {task_id}")
                self.logger.info(f"优化率: {optimization_result['optimization_summary'].get('optimization_rate', 0)}%")
            else:
                self.logger.warning(f"AI优化失败，保持原始字幕: {task_id}")

            return recognition_result

        except Exception as e:
            self.logger.error(f"AI优化失败 {task_id}: {str(e)}")
            # 如果AI优化失败，不影响整个流程，保持原始字幕
            self.logger.warning(f"AI优化失败，保持原始字幕: {task_id}")
            return recognition_result
    
    def get_task(self, task_id):
        """获取任务信息"""
        with self.task_lock:
            return self.tasks.get(task_id, None)
    
    def update_task(self, task_id, **kwargs):
        """更新任务状态"""
        with self.task_lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                for key, value in kwargs.items():
                    task[key] = value
                task['updated_time'] = datetime.now()
                return True
        return False
    
    def cancel_task(self, task_id):
        """取消任务"""
        try:
            task = self.get_task(task_id)
            if not task:
                return False
            
            if task['status'] in ['completed', 'failed', 'cancelled']:
                return False
            
            # 更新任务状态
            self.update_task(task_id, 
                           status='cancelled', 
                           message='任务已取消')
            
            # 等待处理线程结束
            if task_id in self.processing_threads:
                thread = self.processing_threads[task_id]
                if thread.is_alive():
                    # 给线程一些时间来检查取消状态
                    thread.join(timeout=5)
            
            self.logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消任务失败: {str(e)}")
            return False
    
    def get_active_tasks(self):
        """获取活跃任务列表"""
        with self.task_lock:
            active_tasks = []
            for task_id, task in self.tasks.items():
                if task['status'] in ['created', 'processing']:
                    active_tasks.append({
                        'id': task_id,
                        'status': task['status'],
                        'progress': task['progress'],
                        'created_time': task['created_time']
                    })
            return active_tasks
    
    def cleanup_completed_tasks(self, max_age_hours=24):
        """清理已完成的任务"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            with self.task_lock:
                completed_tasks = []
                for task_id, task in self.tasks.items():
                    if (task['status'] in ['completed', 'failed', 'cancelled'] and 
                        task['updated_time'] < cutoff_time):
                        completed_tasks.append(task_id)
                
                for task_id in completed_tasks:
                    del self.tasks[task_id]
                    self.logger.info(f"清理已完成任务: {task_id}")
                
                return len(completed_tasks)
                
        except Exception as e:
            self.logger.error(f"清理已完成任务失败: {str(e)}")
            return 0
