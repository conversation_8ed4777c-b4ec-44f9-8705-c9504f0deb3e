# -*- coding: utf-8 -*-
"""
任务处理器模块
负责异步处理音视频转字幕任务
"""

import os
import logging
import threading
import time
from datetime import datetime, timedelta

from audio_processor import process_media_file

class TaskProcessor:
    """任务处理器"""
    
    def __init__(self, app, tasks_dict, task_lock):
        self.app = app
        self.tasks = tasks_dict
        self.task_lock = task_lock
        self.logger = logging.getLogger(__name__)
        self.processing_threads = {}
        
    def start_processing(self, task_id):
        """开始处理任务"""
        try:
            task = self.get_task(task_id)
            if not task:
                self.logger.error(f"任务不存在: {task_id}")
                return False
            
            if task['status'] != 'created':
                self.logger.warning(f"任务状态不正确: {task_id}, 状态: {task['status']}")
                return False
            
            # 创建处理线程
            thread = threading.Thread(
                target=self._process_task,
                args=(task_id,),
                daemon=True
            )
            
            self.processing_threads[task_id] = thread
            thread.start()
            
            self.logger.info(f"开始处理任务: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动任务处理失败: {str(e)}")
            return False
    
    def _process_task(self, task_id):
        """处理任务的主函数"""
        try:
            # 更新任务状态
            self.update_task(task_id, 
                           status='processing', 
                           progress=5, 
                           message='开始处理文件...')
            
            task = self.get_task(task_id)
            if not task:
                return
            
            file_info = task['file_info']
            file_path = file_info['file_path']
            options = task.get('options', {})
            
            # 步骤1: 文件预处理
            self.update_task(task_id, 
                           progress=10, 
                           message='正在预处理文件...')
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise Exception("源文件不存在")
            
            # 步骤2: 音频处理
            self.update_task(task_id, 
                           progress=20, 
                           message='正在处理音频...')
            
            # 处理媒体文件
            with self.app.app_context():
                temp_dir = self.app.config['TEMP_FOLDER']
                
            media_result = process_media_file(file_path, temp_dir)
            audio_path = media_result['audio_path']
            audio_info = media_result['audio_info']
            processor = media_result['processor']
            
            # 更新任务信息
            self.update_task(task_id, 
                           progress=30, 
                           message='音频处理完成，准备语音识别...')
            
            # 步骤3: 语音识别（这里先模拟，后续会实现）
            self.update_task(task_id, 
                           progress=40, 
                           message='正在进行语音识别...')
            
            # 模拟语音识别过程
            self._simulate_speech_recognition(task_id, audio_path, audio_info)
            
            # 步骤4: AI优化（如果启用）
            if options.get('ai_optimize', True):
                self.update_task(task_id, 
                               progress=80, 
                               message='正在进行AI优化...')
                
                # 模拟AI优化过程
                self._simulate_ai_optimization(task_id)
            
            # 步骤5: 完成处理
            self.update_task(task_id, 
                           progress=100, 
                           status='completed',
                           message='处理完成')
            
            # 生成结果
            result = {
                'audio_info': audio_info,
                'original_subtitle_path': None,  # 后续实现
                'optimized_subtitle_path': None,  # 后续实现
                'processing_time': (datetime.now() - task['created_time']).total_seconds(),
                'subtitle_count': 0,  # 后续实现
                'duration': audio_info.get('duration', 0)
            }
            
            self.update_task(task_id, result=result)
            
            # 清理临时文件
            try:
                if audio_path != file_path:  # 如果是转换后的音频文件
                    processor.cleanup_temp_files([audio_path])
            except:
                pass
            
            self.logger.info(f"任务处理完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"任务处理失败 {task_id}: {str(e)}")
            self.update_task(task_id, 
                           status='failed', 
                           error=str(e),
                           message=f'处理失败: {str(e)}')
        finally:
            # 清理线程记录
            if task_id in self.processing_threads:
                del self.processing_threads[task_id]
    
    def _simulate_speech_recognition(self, task_id, audio_path, audio_info):
        """模拟语音识别过程"""
        try:
            duration = audio_info.get('duration', 60)
            
            # 根据音频时长模拟处理时间
            simulation_steps = min(int(duration / 10), 20)  # 最多20步
            
            for i in range(simulation_steps):
                # 检查任务是否被取消
                task = self.get_task(task_id)
                if not task or task['status'] == 'cancelled':
                    return
                
                progress = 40 + (i + 1) * (35 / simulation_steps)
                self.update_task(task_id, 
                               progress=int(progress),
                               message=f'语音识别进行中... ({i+1}/{simulation_steps})')
                
                time.sleep(0.5)  # 模拟处理时间
            
            self.logger.info(f"语音识别模拟完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"语音识别模拟失败: {str(e)}")
            raise
    
    def _simulate_ai_optimization(self, task_id):
        """模拟AI优化过程"""
        try:
            # 模拟AI优化步骤
            optimization_steps = 5
            
            for i in range(optimization_steps):
                # 检查任务是否被取消
                task = self.get_task(task_id)
                if not task or task['status'] == 'cancelled':
                    return
                
                progress = 80 + (i + 1) * (15 / optimization_steps)
                self.update_task(task_id, 
                               progress=int(progress),
                               message=f'AI优化进行中... ({i+1}/{optimization_steps})')
                
                time.sleep(0.3)  # 模拟处理时间
            
            self.logger.info(f"AI优化模拟完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"AI优化模拟失败: {str(e)}")
            raise
    
    def get_task(self, task_id):
        """获取任务信息"""
        with self.task_lock:
            return self.tasks.get(task_id, None)
    
    def update_task(self, task_id, **kwargs):
        """更新任务状态"""
        with self.task_lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                for key, value in kwargs.items():
                    task[key] = value
                task['updated_time'] = datetime.now()
                return True
        return False
    
    def cancel_task(self, task_id):
        """取消任务"""
        try:
            task = self.get_task(task_id)
            if not task:
                return False
            
            if task['status'] in ['completed', 'failed', 'cancelled']:
                return False
            
            # 更新任务状态
            self.update_task(task_id, 
                           status='cancelled', 
                           message='任务已取消')
            
            # 等待处理线程结束
            if task_id in self.processing_threads:
                thread = self.processing_threads[task_id]
                if thread.is_alive():
                    # 给线程一些时间来检查取消状态
                    thread.join(timeout=5)
            
            self.logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消任务失败: {str(e)}")
            return False
    
    def get_active_tasks(self):
        """获取活跃任务列表"""
        with self.task_lock:
            active_tasks = []
            for task_id, task in self.tasks.items():
                if task['status'] in ['created', 'processing']:
                    active_tasks.append({
                        'id': task_id,
                        'status': task['status'],
                        'progress': task['progress'],
                        'created_time': task['created_time']
                    })
            return active_tasks
    
    def cleanup_completed_tasks(self, max_age_hours=24):
        """清理已完成的任务"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            with self.task_lock:
                completed_tasks = []
                for task_id, task in self.tasks.items():
                    if (task['status'] in ['completed', 'failed', 'cancelled'] and 
                        task['updated_time'] < cutoff_time):
                        completed_tasks.append(task_id)
                
                for task_id in completed_tasks:
                    del self.tasks[task_id]
                    self.logger.info(f"清理已完成任务: {task_id}")
                
                return len(completed_tasks)
                
        except Exception as e:
            self.logger.error(f"清理已完成任务失败: {str(e)}")
            return 0
